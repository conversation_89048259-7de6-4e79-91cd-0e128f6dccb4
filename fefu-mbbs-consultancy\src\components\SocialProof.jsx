import React, { useState, useEffect } from 'react'
import { Bell, CheckCircle, Users, MapPin, Clock } from 'lucide-react'

const SocialProof = () => {
  const [currentNotification, setCurrentNotification] = useState(0)
  const [showNotification, setShowNotification] = useState(true)

  const notifications = [
    {
      id: 1,
      type: 'admission',
      message: 'Priya S. from Delhi got FEFU admission',
      time: '2 minutes ago',
      icon: <CheckCircle size={16} />,
      location: 'Delhi'
    },
    {
      id: 2,
      type: 'visa',
      message: 'Rahul M. visa approved for FEFU',
      time: '15 minutes ago',
      icon: <CheckCircle size={16} />,
      location: 'Mumbai'
    },
    {
      id: 3,
      type: 'consultation',
      message: 'Ananya K. booked free consultation',
      time: '32 minutes ago',
      icon: <Users size={16} />,
      location: 'Bangalore'
    },
    {
      id: 4,
      type: 'admission',
      message: '<PERSON><PERSON><PERSON>. started MBBS at FEFU',
      time: '1 hour ago',
      icon: <CheckCircle size={16} />,
      location: 'Chennai'
    },
    {
      id: 5,
      type: 'evaluation',
      message: '<PERSON><PERSON><PERSON> <PERSON><PERSON> completed NEET evaluation',
      time: '1 hour ago',
      icon: <Users size={16} />,
      location: 'Pune'
    },
    {
      id: 6,
      type: 'visa',
      message: 'Karthik V. received visa approval',
      time: '2 hours ago',
      icon: <CheckCircle size={16} />,
      location: 'Hyderabad'
    }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentNotification((prev) => (prev + 1) % notifications.length)
      setShowNotification(true)
    }, 5000) // Change notification every 5 seconds

    return () => clearInterval(interval)
  }, [notifications.length])

  const hideNotification = () => {
    setShowNotification(false)
  }

  const getNotificationColor = (type) => {
    switch (type) {
      case 'admission': return '#10b981'
      case 'visa': return '#3b82f6'
      case 'consultation': return '#f59e0b'
      case 'evaluation': return '#8b5cf6'
      default: return '#6b7280'
    }
  }

  return (
    <>
      {/* Floating Social Proof Notification */}
      {showNotification && (
        <div className="social-proof-notification">
          <div className="notification-content">
            <div className="notification-icon" style={{ color: getNotificationColor(notifications[currentNotification].type) }}>
              {notifications[currentNotification].icon}
            </div>
            
            <div className="notification-details">
              <p className="notification-message">
                {notifications[currentNotification].message}
              </p>
              <div className="notification-meta">
                <MapPin size={12} />
                <span>{notifications[currentNotification].location}</span>
                <Clock size={12} />
                <span>{notifications[currentNotification].time}</span>
              </div>
            </div>
          </div>
          
          <button className="notification-close" onClick={hideNotification}>
            ×
          </button>
        </div>
      )}

      {/* Live Activity Feed */}
      <section className="live-activity">
        <div className="container">
          <div className="activity-header">
            <Bell className="activity-icon" size={24} />
            <h3>Live Activity Feed</h3>
            <span className="live-indicator">🔴 LIVE</span>
          </div>

          <div className="activity-feed">
            {notifications.map((notification, index) => (
              <div 
                key={notification.id} 
                className={`activity-item ${index === currentNotification ? 'highlighted' : ''}`}
              >
                <div className="activity-dot" style={{ backgroundColor: getNotificationColor(notification.type) }}></div>
                <div className="activity-content">
                  <p>{notification.message}</p>
                  <div className="activity-meta">
                    <MapPin size={12} />
                    <span>{notification.location}</span>
                    <Clock size={12} />
                    <span>{notification.time}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="activity-stats">
            <div className="stat-item">
              <span className="stat-number">47</span>
              <span className="stat-label">Students online now</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">12</span>
              <span className="stat-label">Consultations today</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">3</span>
              <span className="stat-label">Admissions this week</span>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Indicators */}
      <section className="trust-indicators">
        <div className="container">
          <div className="indicators-grid">
            <div className="indicator-item">
              <div className="indicator-icon">
                <CheckCircle size={24} />
              </div>
              <div className="indicator-content">
                <h4>500+ Success Stories</h4>
                <p>Students successfully placed at FEFU</p>
              </div>
            </div>

            <div className="indicator-item">
              <div className="indicator-icon">
                <Users size={24} />
              </div>
              <div className="indicator-content">
                <h4>98% Success Rate</h4>
                <p>Admission and visa approval rate</p>
              </div>
            </div>

            <div className="indicator-item">
              <div className="indicator-icon">
                <Bell size={24} />
              </div>
              <div className="indicator-content">
                <h4>24/7 Support</h4>
                <p>Round-the-clock student assistance</p>
              </div>
            </div>

            <div className="indicator-item">
              <div className="indicator-icon">
                <MapPin size={24} />
              </div>
              <div className="indicator-content">
                <h4>Pan-India Presence</h4>
                <p>Students from all states</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}

export default SocialProof
