import React, { useState } from 'react'
import { Calculator, DollarSign, PiggyBank, TrendingUp } from 'lucide-react'

const CostCalculator = () => {
  const [inputs, setInputs] = useState({
    duration: '6',
    accommodation: 'hostel',
    lifestyle: 'moderate',
    currency: 'inr'
  })
  const [result, setResult] = useState(null)

  const costs = {
    tuition: {
      annual: 5000, // USD
      total: 30000
    },
    accommodation: {
      hostel: 1200, // USD per year
      private: 2400,
      shared: 1800
    },
    living: {
      basic: 2400, // USD per year
      moderate: 3600,
      comfortable: 4800
    },
    oneTime: {
      visa: 500,
      travel: 800,
      medical: 300,
      documents: 200
    }
  }

  const exchangeRates = {
    usd: 1,
    inr: 83,
    eur: 0.85
  }

  const handleInputChange = (e) => {
    setInputs({
      ...inputs,
      [e.target.name]: e.target.value
    })
  }

  const calculateCosts = () => {
    const years = parseInt(inputs.duration)
    const accommodationCost = costs.accommodation[inputs.accommodation] * years
    const livingCost = costs.living[inputs.lifestyle] * years
    const tuitionCost = costs.tuition.annual * years
    const oneTimeCosts = Object.values(costs.oneTime).reduce((sum, cost) => sum + cost, 0)
    
    const totalUSD = tuitionCost + accommodationCost + livingCost + oneTimeCosts
    const rate = exchangeRates[inputs.currency]
    const totalConverted = totalUSD * rate

    const breakdown = {
      tuition: tuitionCost * rate,
      accommodation: accommodationCost * rate,
      living: livingCost * rate,
      oneTime: oneTimeCosts * rate,
      total: totalConverted
    }

    setResult({
      breakdown,
      currency: inputs.currency,
      years,
      monthlyAverage: (totalConverted - (oneTimeCosts * rate)) / (years * 12)
    })
  }

  const formatCurrency = (amount, currency) => {
    const symbols = { usd: '$', inr: '₹', eur: '€' }
    const formatted = new Intl.NumberFormat('en-US').format(Math.round(amount))
    return `${symbols[currency]}${formatted}`
  }

  return (
    <section className="cost-calculator">
      <div className="container">
        <div className="calculator-content">
          <div className="calculator-header">
            <Calculator className="calculator-icon" size={40} />
            <h3>MBBS Cost Calculator</h3>
            <p>Calculate your total investment for MBBS at FEFU</p>
          </div>

          <div className="calculator-form">
            <div className="form-grid">
              <div className="form-group">
                <label htmlFor="duration">Course Duration</label>
                <select
                  id="duration"
                  name="duration"
                  value={inputs.duration}
                  onChange={handleInputChange}
                >
                  <option value="6">6 Years (Complete MBBS)</option>
                  <option value="5">5 Years (Without Internship)</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="accommodation">Accommodation Type</label>
                <select
                  id="accommodation"
                  name="accommodation"
                  value={inputs.accommodation}
                  onChange={handleInputChange}
                >
                  <option value="hostel">University Hostel</option>
                  <option value="shared">Shared Apartment</option>
                  <option value="private">Private Apartment</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="lifestyle">Lifestyle</label>
                <select
                  id="lifestyle"
                  name="lifestyle"
                  value={inputs.lifestyle}
                  onChange={handleInputChange}
                >
                  <option value="basic">Basic (₹20,000/month)</option>
                  <option value="moderate">Moderate (₹30,000/month)</option>
                  <option value="comfortable">Comfortable (₹40,000/month)</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="currency">Currency</label>
                <select
                  id="currency"
                  name="currency"
                  value={inputs.currency}
                  onChange={handleInputChange}
                >
                  <option value="inr">Indian Rupee (₹)</option>
                  <option value="usd">US Dollar ($)</option>
                  <option value="eur">Euro (€)</option>
                </select>
              </div>
            </div>

            <button className="calculate-btn" onClick={calculateCosts}>
              <Calculator size={20} />
              Calculate Total Cost
            </button>
          </div>

          {result && (
            <div className="calculation-result">
              <h4>Your MBBS Investment Breakdown</h4>
              
              <div className="cost-breakdown">
                <div className="cost-item">
                  <div className="cost-icon">
                    <DollarSign size={24} />
                  </div>
                  <div className="cost-details">
                    <span className="cost-label">Tuition Fees ({result.years} years)</span>
                    <span className="cost-amount">{formatCurrency(result.breakdown.tuition, result.currency)}</span>
                  </div>
                </div>

                <div className="cost-item">
                  <div className="cost-icon">
                    <PiggyBank size={24} />
                  </div>
                  <div className="cost-details">
                    <span className="cost-label">Accommodation</span>
                    <span className="cost-amount">{formatCurrency(result.breakdown.accommodation, result.currency)}</span>
                  </div>
                </div>

                <div className="cost-item">
                  <div className="cost-icon">
                    <TrendingUp size={24} />
                  </div>
                  <div className="cost-details">
                    <span className="cost-label">Living Expenses</span>
                    <span className="cost-amount">{formatCurrency(result.breakdown.living, result.currency)}</span>
                  </div>
                </div>

                <div className="cost-item">
                  <div className="cost-icon">
                    <Calculator size={24} />
                  </div>
                  <div className="cost-details">
                    <span className="cost-label">One-time Costs</span>
                    <span className="cost-amount">{formatCurrency(result.breakdown.oneTime, result.currency)}</span>
                  </div>
                </div>
              </div>

              <div className="total-cost">
                <div className="total-amount">
                  <span className="total-label">Total Investment</span>
                  <span className="total-value">{formatCurrency(result.breakdown.total, result.currency)}</span>
                </div>
                <div className="monthly-average">
                  <span>Average Monthly: {formatCurrency(result.monthlyAverage, result.currency)}</span>
                </div>
              </div>

              <div className="cost-benefits">
                <h5>Why This Investment is Worth It:</h5>
                <ul>
                  <li>WHO & MCI recognized degree</li>
                  <li>Lower cost compared to private Indian colleges</li>
                  <li>International exposure and experience</li>
                  <li>High-quality medical education</li>
                  <li>Better doctor-patient ratio for practical training</li>
                </ul>
              </div>

              <div className="calculator-cta">
                <button 
                  className="btn-primary"
                  onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}
                >
                  Get Detailed Cost Breakdown
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}

export default CostCalculator
