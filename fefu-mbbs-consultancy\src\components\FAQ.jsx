import React, { useState } from 'react'
import { ChevronDown, ChevronUp, HelpCircle, Search } from 'lucide-react'

const FAQ = () => {
  const [activeCategory, setActiveCategory] = useState('general')
  const [openFAQ, setOpenFAQ] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')

  const faqCategories = {
    general: {
      title: "General Information",
      faqs: [
        {
          question: "Is FEFU recognized by MCI/NMC and WHO?",
          answer: "Yes, Far Eastern Federal University (FEFU) is fully recognized by the Medical Council of India (MCI), now known as National Medical Commission (NMC), and the World Health Organization (WHO). This recognition allows Indian students to practice medicine in India after clearing the FMGE/NEXT exam."
        },
        {
          question: "What is the duration of MBBS at FEFU?",
          answer: "The MBBS program at FEFU is 6 years long, which includes 5 years of academic study and 1 year of mandatory internship. The curriculum is designed according to international standards and includes both theoretical and practical training."
        },
        {
          question: "Is the medium of instruction English?",
          answer: "Yes, FEFU offers MBBS programs in English medium specifically designed for international students. All lectures, practicals, and examinations are conducted in English, making it easier for Indian students to understand and excel."
        },
        {
          question: "What are the eligibility criteria for FEFU MBBS?",
          answer: "Students must have completed 12th grade with Physics, Chemistry, and Biology with minimum 50% marks (40% for SC/ST/OBC). NEET qualification is mandatory for Indian students. The minimum age should be 17 years and maximum 25 years as of December 31st of the admission year."
        }
      ]
    },
    admission: {
      title: "Admission Process",
      faqs: [
        {
          question: "What NEET score is required for FEFU admission?",
          answer: "There is no specific NEET score requirement for FEFU admission. Students need to qualify NEET (pass the exam) to be eligible. However, a higher NEET score can help in the admission process and scholarship opportunities."
        },
        {
          question: "Is there any entrance exam for FEFU?",
          answer: "No, FEFU does not conduct any separate entrance exam. Admission is based on your 12th grade marks and NEET qualification. The admission process is direct and straightforward."
        },
        {
          question: "When does the admission process start?",
          answer: "The admission process for FEFU typically starts in June-July after NEET results are declared. The academic session begins in September. We recommend starting the application process as early as possible to secure your seat."
        },
        {
          question: "What documents are required for admission?",
          answer: "Required documents include: NEET scorecard, 12th mark sheet, 10th mark sheet, passport, birth certificate, medical fitness certificate, HIV test report, and passport-size photographs. All documents need to be apostilled for international use."
        }
      ]
    },
    fees: {
      title: "Fees & Expenses",
      faqs: [
        {
          question: "What is the total fee structure for MBBS at FEFU?",
          answer: "The annual tuition fee at FEFU is approximately $5,000 USD. Including accommodation, food, and other expenses, the total annual cost is around $8,000-10,000 USD, which is significantly lower than private medical colleges in India."
        },
        {
          question: "Are there any hidden charges?",
          answer: "No, we maintain complete transparency in our fee structure. All costs including tuition, accommodation, medical insurance, and other mandatory fees are clearly mentioned upfront. There are no hidden charges or surprise fees."
        },
        {
          question: "Can fees be paid in installments?",
          answer: "Yes, the tuition fees can be paid in installments. Typically, fees are paid annually, but arrangements can be made for semester-wise payments. Our team will guide you through the payment process and options available."
        },
        {
          question: "Are scholarships available?",
          answer: "Yes, FEFU offers merit-based scholarships for outstanding students. Scholarships are awarded based on academic performance and NEET scores. Our consultancy also provides information about various government and private scholarship opportunities."
        }
      ]
    },
    visa: {
      title: "Visa & Travel",
      faqs: [
        {
          question: "How long does the visa process take?",
          answer: "The Russian student visa process typically takes 15-20 working days after document submission. However, we recommend starting the visa process at least 2 months before the intended travel date to account for any delays or additional requirements."
        },
        {
          question: "What is the visa success rate?",
          answer: "Our visa success rate is 98%. We have experienced visa consultants who ensure all documents are properly prepared and submitted. In rare cases of rejection, we provide full support for reapplication."
        },
        {
          question: "Is medical insurance mandatory?",
          answer: "Yes, medical insurance is mandatory for all international students in Russia. The insurance covers basic medical expenses and emergencies. The annual cost is approximately $200-300 USD and can be arranged through the university or private providers."
        },
        {
          question: "Can parents visit during the course?",
          answer: "Yes, parents can visit Russia on a tourist visa. We can provide invitation letters and assistance for parent visits. Tourist visas are typically valid for 30 days and can be extended if needed."
        }
      ]
    },
    life: {
      title: "Student Life",
      faqs: [
        {
          question: "What is the accommodation like at FEFU?",
          answer: "FEFU provides comfortable hostel accommodation with modern amenities. Rooms are typically shared (2-3 students per room) and include basic furniture, internet connectivity, and common kitchen facilities. Private accommodation options are also available nearby."
        },
        {
          question: "What about food and Indian cuisine?",
          answer: "The university cafeteria offers international cuisine including some Indian dishes. Many Indian restaurants and grocery stores are available in Vladivostok. Students can also cook in hostel kitchens. Our team helps students adapt to local food options."
        },
        {
          question: "Is it safe for Indian students?",
          answer: "Yes, Russia is generally safe for international students. FEFU has a dedicated international student support office, and Vladivostok has a significant Indian student community. We provide 24/7 emergency support and maintain regular contact with our students."
        },
        {
          question: "What about the weather and climate?",
          answer: "Vladivostok has a humid continental climate with cold winters (-10°C to -20°C) and warm summers (20°C to 25°C). Students need appropriate winter clothing, which can be purchased locally or brought from India. The university and hostels have proper heating systems."
        }
      ]
    }
  }

  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? null : index)
  }

  const filteredFAQs = faqCategories[activeCategory].faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <section className="faq-section">
      <div className="container">
        <div className="section-header">
          <HelpCircle className="faq-icon" size={40} />
          <h2>Frequently Asked Questions</h2>
          <p>Get answers to common questions about MBBS at FEFU</p>
        </div>

        <div className="faq-search">
          <div className="search-box">
            <Search size={20} />
            <input
              type="text"
              placeholder="Search for questions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <div className="faq-categories">
          {Object.entries(faqCategories).map(([key, category]) => (
            <button
              key={key}
              className={`category-btn ${activeCategory === key ? 'active' : ''}`}
              onClick={() => setActiveCategory(key)}
            >
              {category.title}
            </button>
          ))}
        </div>

        <div className="faq-content">
          <h3>{faqCategories[activeCategory].title}</h3>
          
          <div className="faq-list">
            {filteredFAQs.map((faq, index) => (
              <div key={index} className="faq-item">
                <button
                  className="faq-question"
                  onClick={() => toggleFAQ(index)}
                >
                  <span>{faq.question}</span>
                  {openFAQ === index ? (
                    <ChevronUp size={20} />
                  ) : (
                    <ChevronDown size={20} />
                  )}
                </button>
                
                {openFAQ === index && (
                  <div className="faq-answer">
                    <p>{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredFAQs.length === 0 && (
            <div className="no-results">
              <p>No questions found matching your search. Try different keywords or browse categories.</p>
            </div>
          )}
        </div>

        <div className="faq-cta">
          <h3>Still Have Questions?</h3>
          <p>Our expert consultants are here to help you with personalized answers</p>
          <div className="cta-buttons">
            <button 
              className="btn-primary"
              onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}
            >
              Ask Our Experts
            </button>
            <button className="btn-secondary">
              Download FAQ Guide
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FAQ
