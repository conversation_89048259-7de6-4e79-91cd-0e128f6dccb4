import React, { useState } from 'react'
import { 
  BookOpen, 
  FileText, 
  Plane, 
  GraduationCap, 
  Stethoscope, 
  Award,
  ChevronRight,
  Clock,
  CheckCircle
} from 'lucide-react'

const StudentJourney = () => {
  const [activeStep, setActiveStep] = useState(0)

  const journeySteps = [
    {
      id: 1,
      title: "Initial Consultation",
      duration: "Day 1",
      icon: <BookOpen size={32} />,
      description: "Free consultation to assess your profile and eligibility",
      details: [
        "NEET score evaluation",
        "Academic background review",
        "Career goal discussion",
        "University recommendation",
        "Cost estimation"
      ],
      status: "completed"
    },
    {
      id: 2,
      title: "Documentation & Application",
      duration: "Week 1-2",
      icon: <FileText size={32} />,
      description: "Complete documentation and university application process",
      details: [
        "Document collection & verification",
        "Application form completion",
        "University submission",
        "Admission letter processing",
        "Fee payment guidance"
      ],
      status: "completed"
    },
    {
      id: 3,
      title: "Visa Processing",
      duration: "Week 3-6",
      icon: <Plane size={32} />,
      description: "Complete visa application and approval process",
      details: [
        "Visa application preparation",
        "Embassy appointment booking",
        "Document submission",
        "Interview preparation",
        "Visa approval & collection"
      ],
      status: "in-progress"
    },
    {
      id: 4,
      title: "Pre-Departure Preparation",
      duration: "Week 7-8",
      icon: <GraduationCap size={32} />,
      description: "Complete preparation for your journey to Russia",
      details: [
        "Flight booking assistance",
        "Accommodation arrangement",
        "Pre-departure orientation",
        "Cultural preparation",
        "Essential items checklist"
      ],
      status: "pending"
    },
    {
      id: 5,
      title: "MBBS Studies at FEFU",
      duration: "6 Years",
      icon: <Stethoscope size={32} />,
      description: "Complete your MBBS degree with ongoing support",
      details: [
        "Academic support throughout",
        "Regular progress monitoring",
        "Exam preparation assistance",
        "Internship guidance",
        "Career counseling"
      ],
      status: "pending"
    },
    {
      id: 6,
      title: "Post-Graduation Support",
      duration: "Ongoing",
      icon: <Award size={32} />,
      description: "Support for licensing exams and career placement",
      details: [
        "FMGE/NEXT preparation",
        "Licensing exam guidance",
        "Residency application support",
        "Career placement assistance",
        "Alumni network access"
      ],
      status: "pending"
    }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'var(--accent-color)'
      case 'in-progress': return 'var(--secondary-color)'
      case 'pending': return 'var(--border-color)'
      default: return 'var(--border-color)'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircle size={16} />
      case 'in-progress': return <Clock size={16} />
      default: return <div className="pending-dot"></div>
    }
  }

  return (
    <section className="student-journey">
      <div className="container">
        <div className="section-header">
          <h2>Your MBBS Journey at FEFU</h2>
          <p>From consultation to graduation - we support you every step of the way</p>
        </div>

        <div className="journey-content">
          <div className="journey-timeline">
            {journeySteps.map((step, index) => (
              <div 
                key={step.id}
                className={`timeline-step ${activeStep === index ? 'active' : ''}`}
                onClick={() => setActiveStep(index)}
              >
                <div className="step-connector">
                  {index < journeySteps.length - 1 && (
                    <div className="connector-line"></div>
                  )}
                </div>
                
                <div className="step-marker">
                  <div 
                    className="step-icon"
                    style={{ backgroundColor: getStatusColor(step.status) }}
                  >
                    {step.icon}
                  </div>
                  <div className="step-status">
                    {getStatusIcon(step.status)}
                  </div>
                </div>

                <div className="step-content">
                  <div className="step-header">
                    <h4>{step.title}</h4>
                    <span className="step-duration">{step.duration}</span>
                  </div>
                  <p className="step-description">{step.description}</p>
                  
                  {activeStep === index && (
                    <div className="step-details">
                      <h5>What's Included:</h5>
                      <ul>
                        {step.details.map((detail, idx) => (
                          <li key={idx}>{detail}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                <ChevronRight 
                  className="step-arrow" 
                  size={20}
                  style={{ 
                    transform: activeStep === index ? 'rotate(90deg)' : 'rotate(0deg)',
                    color: activeStep === index ? 'var(--primary-color)' : 'var(--text-light)'
                  }}
                />
              </div>
            ))}
          </div>

          <div className="journey-summary">
            <div className="summary-card">
              <h3>Journey Highlights</h3>
              
              <div className="highlight-stats">
                <div className="stat">
                  <div className="stat-number">8</div>
                  <div className="stat-label">Weeks to Departure</div>
                </div>
                <div className="stat">
                  <div className="stat-number">6</div>
                  <div className="stat-label">Years of Study</div>
                </div>
                <div className="stat">
                  <div className="stat-number">24/7</div>
                  <div className="stat-label">Support Available</div>
                </div>
              </div>

              <div className="success-metrics">
                <h4>Our Track Record</h4>
                <div className="metrics-list">
                  <div className="metric-item">
                    <CheckCircle size={16} />
                    <span>98% Visa Approval Rate</span>
                  </div>
                  <div className="metric-item">
                    <CheckCircle size={16} />
                    <span>500+ Students Successfully Placed</span>
                  </div>
                  <div className="metric-item">
                    <CheckCircle size={16} />
                    <span>95% Student Satisfaction Rate</span>
                  </div>
                  <div className="metric-item">
                    <CheckCircle size={16} />
                    <span>24/7 Emergency Support</span>
                  </div>
                </div>
              </div>

              <div className="journey-cta">
                <h4>Ready to Start Your Journey?</h4>
                <p>Join hundreds of successful students who chose FEFU for their MBBS</p>
                <button 
                  className="btn-primary"
                  onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}
                >
                  Begin Your Journey Today
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="journey-testimonial">
          <div className="testimonial-content">
            <div className="testimonial-quote">
              <h4>"The journey was smooth and well-guided. Every step was clearly explained, and the support team was always available when I needed help."</h4>
              <div className="testimonial-author">
                <img src="/api/placeholder/50/50" alt="Student" />
                <div>
                  <strong>Priya Sharma</strong>
                  <span>Currently in 3rd Year MBBS at FEFU</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default StudentJourney
