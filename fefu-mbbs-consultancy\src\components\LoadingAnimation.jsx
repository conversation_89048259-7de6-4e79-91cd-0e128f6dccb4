import React, { useState, useEffect } from 'react'
import { GraduationCap, Heart, Stethoscope } from 'lucide-react'

const LoadingAnimation = () => {
  const [isLoading, setIsLoading] = useState(true)
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    // Simulate loading progress
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setTimeout(() => setIsLoading(false), 500)
          return 100
        }
        return prev + 10
      })
    }, 200)

    return () => clearInterval(interval)
  }, [])

  if (!isLoading) return null

  return (
    <div className="loading-overlay">
      <div className="loading-container">
        <div className="loading-logo">
          <div className="logo-animation">
            <GraduationCap size={48} className="main-icon" />
            <Heart size={24} className="heart-icon" />
            <Stethoscope size={32} className="stethoscope-icon" />
          </div>
          <h2>FEFU MBBS Consultancy</h2>
          <p>Your Gateway to Medical Excellence</p>
        </div>

        <div className="loading-progress">
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
          <span className="progress-text">{progress}%</span>
        </div>

        <div className="loading-messages">
          {progress < 30 && <p>Initializing your medical journey...</p>}
          {progress >= 30 && progress < 60 && <p>Loading FEFU information...</p>}
          {progress >= 60 && progress < 90 && <p>Preparing consultation tools...</p>}
          {progress >= 90 && <p>Almost ready! Welcome to your future...</p>}
        </div>

        <div className="loading-dots">
          <div className="dot"></div>
          <div className="dot"></div>
          <div className="dot"></div>
        </div>
      </div>
    </div>
  )
}

export default LoadingAnimation
