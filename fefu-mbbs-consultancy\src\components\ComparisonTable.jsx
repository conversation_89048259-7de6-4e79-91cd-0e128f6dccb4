import React, { useState } from 'react'
import { Check, X, Star, Award, DollarSign, Clock } from 'lucide-react'

const ComparisonTable = () => {
  const [activeTab, setActiveTab] = useState('cost')

  const comparisonData = {
    cost: {
      title: "Cost Comparison",
      subtitle: "Annual fees comparison with other options",
      data: [
        {
          feature: "Tuition Fees (Annual)",
          fefu: "$5,000",
          privateIndia: "$25,000 - $50,000",
          otherRussian: "$6,000 - $8,000",
          advantage: "fefu"
        },
        {
          feature: "Accommodation",
          fefu: "$1,200",
          privateIndia: "$3,000 - $6,000",
          otherRussian: "$1,500 - $2,500",
          advantage: "fefu"
        },
        {
          feature: "Living Expenses",
          fefu: "$2,400",
          privateIndia: "$4,800 - $7,200",
          otherRussian: "$3,000 - $4,000",
          advantage: "fefu"
        },
        {
          feature: "Total Annual Cost",
          fefu: "$8,600",
          privateIndia: "$32,800 - $63,200",
          otherRussian: "$10,500 - $14,500",
          advantage: "fefu"
        }
      ]
    },
    quality: {
      title: "Education Quality",
      subtitle: "Academic standards and recognition",
      data: [
        {
          feature: "WHO Recognition",
          fefu: true,
          privateIndia: true,
          otherRussian: "Varies",
          advantage: "tie"
        },
        {
          feature: "MCI/NMC Recognition",
          fefu: true,
          privateIndia: true,
          otherRussian: "Most",
          advantage: "tie"
        },
        {
          feature: "English Medium",
          fefu: true,
          privateIndia: true,
          otherRussian: "Limited",
          advantage: "fefu"
        },
        {
          feature: "Clinical Exposure",
          fefu: "Excellent",
          privateIndia: "Limited",
          otherRussian: "Good",
          advantage: "fefu"
        },
        {
          feature: "Research Opportunities",
          fefu: "Extensive",
          privateIndia: "Moderate",
          otherRussian: "Good",
          advantage: "fefu"
        }
      ]
    },
    admission: {
      title: "Admission Process",
      subtitle: "Ease of admission and requirements",
      data: [
        {
          feature: "NEET Requirement",
          fefu: "Qualifying",
          privateIndia: "High Score",
          otherRussian: "Qualifying",
          advantage: "tie"
        },
        {
          feature: "Donation/Capitation",
          fefu: false,
          privateIndia: "₹50L - ₹2Cr",
          otherRussian: false,
          advantage: "fefu"
        },
        {
          feature: "Admission Process",
          fefu: "Direct",
          privateIndia: "Complex",
          otherRussian: "Moderate",
          advantage: "fefu"
        },
        {
          feature: "Seat Availability",
          fefu: "Good",
          privateIndia: "Limited",
          otherRussian: "Moderate",
          advantage: "fefu"
        }
      ]
    }
  }

  const renderValue = (value, advantage, column) => {
    if (typeof value === 'boolean') {
      return value ? (
        <Check className="check-icon" size={20} />
      ) : (
        <X className="x-icon" size={20} />
      )
    }

    const isAdvantage = advantage === column || (advantage === 'tie' && column === 'fefu')
    
    return (
      <span className={isAdvantage ? 'advantage-value' : 'normal-value'}>
        {value}
        {isAdvantage && advantage !== 'tie' && <Star size={14} className="advantage-star" />}
      </span>
    )
  }

  return (
    <section className="comparison-table">
      <div className="container">
        <div className="section-header">
          <h2>Why Choose FEFU Over Other Options?</h2>
          <p>Compare FEFU with private Indian colleges and other Russian universities</p>
        </div>

        <div className="comparison-tabs">
          <button 
            className={`tab-btn ${activeTab === 'cost' ? 'active' : ''}`}
            onClick={() => setActiveTab('cost')}
          >
            <DollarSign size={20} />
            Cost Analysis
          </button>
          <button 
            className={`tab-btn ${activeTab === 'quality' ? 'active' : ''}`}
            onClick={() => setActiveTab('quality')}
          >
            <Award size={20} />
            Education Quality
          </button>
          <button 
            className={`tab-btn ${activeTab === 'admission' ? 'active' : ''}`}
            onClick={() => setActiveTab('admission')}
          >
            <Clock size={20} />
            Admission Process
          </button>
        </div>

        <div className="comparison-content">
          <div className="comparison-header">
            <h3>{comparisonData[activeTab].title}</h3>
            <p>{comparisonData[activeTab].subtitle}</p>
          </div>

          <div className="comparison-table-wrapper">
            <table className="comparison-table-grid">
              <thead>
                <tr>
                  <th className="feature-column">Features</th>
                  <th className="fefu-column">
                    <div className="column-header">
                      <Star className="fefu-star" size={20} />
                      <span>FEFU</span>
                      <span className="recommended-badge">Recommended</span>
                    </div>
                  </th>
                  <th className="comparison-column">Private Indian Colleges</th>
                  <th className="comparison-column">Other Russian Universities</th>
                </tr>
              </thead>
              <tbody>
                {comparisonData[activeTab].data.map((row, index) => (
                  <tr key={index} className={row.advantage === 'fefu' ? 'fefu-advantage' : ''}>
                    <td className="feature-cell">
                      <strong>{row.feature}</strong>
                    </td>
                    <td className="fefu-cell">
                      {renderValue(row.fefu, row.advantage, 'fefu')}
                    </td>
                    <td className="comparison-cell">
                      {renderValue(row.privateIndia, row.advantage, 'privateIndia')}
                    </td>
                    <td className="comparison-cell">
                      {renderValue(row.otherRussian, row.advantage, 'otherRussian')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="comparison-summary">
          <div className="summary-cards">
            <div className="summary-card cost-savings">
              <h4>💰 Cost Savings</h4>
              <p>Save up to <strong>₹2 Crores</strong> compared to private Indian colleges</p>
            </div>
            
            <div className="summary-card quality-education">
              <h4>🎓 Quality Education</h4>
              <p>WHO & MCI recognized degree with excellent clinical exposure</p>
            </div>
            
            <div className="summary-card easy-admission">
              <h4>✅ Easy Admission</h4>
              <p>No donations, direct admission with qualifying NEET score</p>
            </div>
          </div>
        </div>

        <div className="comparison-cta">
          <h3>Ready to Make the Smart Choice?</h3>
          <p>Join thousands of students who chose FEFU for their MBBS journey</p>
          <div className="cta-buttons">
            <button 
              className="btn-primary"
              onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}
            >
              Get Detailed Comparison Report
            </button>
            <button 
              className="btn-secondary"
              onClick={() => document.getElementById('neet-evaluator').scrollIntoView({ behavior: 'smooth' })}
            >
              Check Your Eligibility
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ComparisonTable
