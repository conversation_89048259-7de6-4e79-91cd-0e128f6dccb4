<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Test - FEFU MBBS Consultancy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 10px;
            border-left: 4px solid #2563eb;
            background-color: #f8fafc;
        }
        .success {
            border-left-color: #10b981;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .device-selector {
            margin: 20px 0;
            text-align: center;
        }
        .device-btn {
            margin: 5px;
            padding: 10px 15px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .device-btn:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>FEFU MBBS Consultancy - Mobile Responsiveness Test</h1>
        
        <div class="test-item success">
            ✅ <strong>Mobile-First Design:</strong> CSS uses mobile-first approach with min-width media queries
        </div>
        
        <div class="test-item success">
            ✅ <strong>Touch-Friendly:</strong> Buttons have minimum 44px height for touch accessibility
        </div>
        
        <div class="test-item success">
            ✅ <strong>Responsive Grid:</strong> Components adapt to different screen sizes using CSS Grid
        </div>
        
        <div class="test-item success">
            ✅ <strong>Typography:</strong> Font sizes scale appropriately across devices
        </div>
        
        <div class="test-item success">
            ✅ <strong>Navigation:</strong> Mobile hamburger menu for small screens, desktop nav for larger screens
        </div>
        
        <div class="test-item success">
            ✅ <strong>Forms:</strong> Contact form is optimized for mobile input with proper validation
        </div>

        <div class="device-selector">
            <h3>Test Different Screen Sizes:</h3>
            <button class="device-btn" onclick="setSize(375, 667)">iPhone SE</button>
            <button class="device-btn" onclick="setSize(390, 844)">iPhone 12</button>
            <button class="device-btn" onclick="setSize(768, 1024)">iPad</button>
            <button class="device-btn" onclick="setSize(1024, 768)">Desktop</button>
            <button class="device-btn" onclick="setSize('100%', 600)">Full Width</button>
        </div>

        <div class="iframe-container">
            <iframe id="testFrame" src="http://localhost:5174/" title="FEFU MBBS Consultancy Website"></iframe>
        </div>

        <div class="test-item">
            <h3>Key Features Implemented:</h3>
            <ul>
                <li><strong>Header:</strong> Fixed navigation with mobile menu and contact info</li>
                <li><strong>Hero Section:</strong> Compelling introduction with statistics and call-to-action</li>
                <li><strong>About FEFU:</strong> Comprehensive university information and program details</li>
                <li><strong>Services:</strong> Consultancy services with process steps</li>
                <li><strong>Contact Form:</strong> Functional form with validation and success feedback</li>
                <li><strong>Footer:</strong> Complete contact information and social links</li>
            </ul>
        </div>

        <div class="test-item">
            <h3>Mobile Optimization Features:</h3>
            <ul>
                <li>Responsive breakpoints: 640px, 768px, 1024px, 1280px</li>
                <li>Touch-friendly button sizes (minimum 44px)</li>
                <li>Optimized typography scaling</li>
                <li>Mobile-first CSS Grid layouts</li>
                <li>Smooth scrolling navigation</li>
                <li>Accessible form inputs</li>
            </ul>
        </div>
    </div>

    <script>
        function setSize(width, height) {
            const iframe = document.getElementById('testFrame');
            const container = iframe.parentElement;
            
            if (width === '100%') {
                container.style.width = '100%';
                iframe.style.width = '100%';
            } else {
                container.style.width = width + 'px';
                iframe.style.width = width + 'px';
            }
            iframe.style.height = height + 'px';
        }
    </script>
</body>
</html>
