import React from 'react'
import { FileText, Plane, Home, GraduationCap, Shield, HeartHandshake } from 'lucide-react'

const Services = () => {
  const services = [
    {
      icon: <FileText size={32} />,
      title: "Admission Guidance",
      description: "Complete assistance with FEFU admission process, document preparation, and application submission."
    },
    {
      icon: <Plane size={32} />,
      title: "Visa Processing",
      description: "Expert visa consultation and processing support to ensure smooth travel to Russia."
    },
    {
      icon: <Home size={32} />,
      title: "Accommodation",
      description: "Help with hostel booking and accommodation arrangements near the university campus."
    },
    {
      icon: <GraduationCap size={32} />,
      title: "Pre-Departure Training",
      description: "Orientation sessions covering Russian culture, language basics, and academic expectations."
    },
    {
      icon: <Shield size={32} />,
      title: "Student Support",
      description: "Ongoing support throughout your academic journey at FEFU, including emergency assistance."
    },
    {
      icon: <HeartHandshake size={32} />,
      title: "Career Counseling",
      description: "Guidance on career opportunities after MBBS completion and licensing exam preparation."
    }
  ]

  return (
    <section id="services" className="services">
      <div className="container">
        <div className="section-header">
          <h2>Our Consultancy Services</h2>
          <p>Comprehensive support for your MBBS journey at FEFU</p>
        </div>

        <div className="services-grid">
          {services.map((service, index) => (
            <div key={index} className="service-card">
              <div className="service-icon">
                {service.icon}
              </div>
              <h3>{service.title}</h3>
              <p>{service.description}</p>
            </div>
          ))}
        </div>

        <div className="process-section">
          <h3>Our Simple Process</h3>
          <div className="process-steps">
            <div className="step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h4>Initial Consultation</h4>
                <p>Free consultation to understand your goals and eligibility</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h4>Document Preparation</h4>
                <p>Assistance with all required documents and application forms</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h4>Application Submission</h4>
                <p>Submit your application to FEFU with our expert guidance</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">4</div>
              <div className="step-content">
                <h4>Visa & Travel</h4>
                <p>Complete visa processing and travel arrangement support</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Services
