import React from 'react'
import { <PERSON>R<PERSON>, Star, Users, GraduationCap } from 'lucide-react'

const Hero = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact')
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="home" className="hero">
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1>
              Your Gateway to <span className="highlight">MBBS in Russia</span>
            </h1>
            <p className="hero-subtitle">
              Expert guidance for admission to Far Eastern Federal University (FEFU) - 
              One of Russia's premier medical institutions offering world-class MBBS education.
            </p>
            
            <div className="hero-stats">
              <div className="stat">
                <div className="stat-icon">
                  <Users size={24} />
                </div>
                <div className="stat-content">
                  <h3>500+</h3>
                  <p>Students Placed</p>
                </div>
              </div>
              <div className="stat">
                <div className="stat-icon">
                  <Star size={24} />
                </div>
                <div className="stat-content">
                  <h3>98%</h3>
                  <p>Success Rate</p>
                </div>
              </div>
              <div className="stat">
                <div className="stat-icon">
                  <GraduationCap size={24} />
                </div>
                <div className="stat-content">
                  <h3>6 Years</h3>
                  <p>MBBS Program</p>
                </div>
              </div>
            </div>

            <div className="hero-buttons">
              <button className="btn-primary" onClick={scrollToContact}>
                Get Free Consultation
                <ArrowRight size={20} />
              </button>
              <button className="btn-secondary" onClick={() => document.getElementById('about').scrollIntoView({ behavior: 'smooth' })}>
                Learn About FEFU
              </button>
            </div>
          </div>
          
          <div className="hero-image">
            <div className="hero-card">
              <h3>Why Choose FEFU?</h3>
              <ul>
                <li>✓ WHO & MCI Recognized</li>
                <li>✓ English Medium Instruction</li>
                <li>✓ Affordable Tuition Fees</li>
                <li>✓ Modern Infrastructure</li>
                <li>✓ International Student Support</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
