import React, { useState } from 'react'
import { User, Lock, Eye, EyeOff, LogIn, UserPlus, BookOpen, FileText, Calendar, MessageSquare } from 'lucide-react'

const StudentPortal = () => {
  const [isLoginOpen, setIsLoginOpen] = useState(false)
  const [isSignupOpen, setIsSignupOpen] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [loginData, setLoginData] = useState({ email: '', password: '' })
  const [signupData, setSignupData] = useState({ 
    name: '', 
    email: '', 
    phone: '', 
    password: '', 
    confirmPassword: '' 
  })

  const toggleLogin = () => {
    setIsLoginOpen(!isLoginOpen)
    setIsSignupOpen(false)
  }

  const toggleSignup = () => {
    setIsSignupOpen(!isSignupOpen)
    setIsLoginOpen(false)
  }

  const handleLoginChange = (e) => {
    setLoginData({
      ...loginData,
      [e.target.name]: e.target.value
    })
  }

  const handleSignupChange = (e) => {
    setSignupData({
      ...signupData,
      [e.target.name]: e.target.value
    })
  }

  const handleLogin = (e) => {
    e.preventDefault()
    console.log('Login attempt:', loginData)
    // Here you would typically handle authentication
    alert('Login functionality would be implemented here')
    setIsLoginOpen(false)
  }

  const handleSignup = (e) => {
    e.preventDefault()
    if (signupData.password !== signupData.confirmPassword) {
      alert('Passwords do not match')
      return
    }
    console.log('Signup attempt:', signupData)
    // Here you would typically handle registration
    alert('Registration functionality would be implemented here')
    setIsSignupOpen(false)
  }

  return (
    <>
      {/* Student Portal Section */}
      <section className="student-portal-section">
        <div className="container">
          <div className="portal-header">
            <User className="portal-icon" size={40} />
            <h2>Student Portal</h2>
            <p>Access your personalized dashboard and track your MBBS journey</p>
          </div>

          <div className="portal-features">
            <div className="feature-grid">
              <div className="feature-card">
                <BookOpen size={32} />
                <h4>Application Tracking</h4>
                <p>Monitor your admission application status in real-time</p>
              </div>
              
              <div className="feature-card">
                <FileText size={32} />
                <h4>Document Management</h4>
                <p>Upload, verify, and manage all your documents securely</p>
              </div>
              
              <div className="feature-card">
                <Calendar size={32} />
                <h4>Schedule Management</h4>
                <p>Book consultations and track important deadlines</p>
              </div>
              
              <div className="feature-card">
                <MessageSquare size={32} />
                <h4>Direct Communication</h4>
                <p>Chat directly with your assigned consultant</p>
              </div>
            </div>
          </div>

          <div className="portal-actions">
            <button className="btn-primary" onClick={toggleLogin}>
              <LogIn size={20} />
              Student Login
            </button>
            <button className="btn-secondary" onClick={toggleSignup}>
              <UserPlus size={20} />
              New Student Registration
            </button>
          </div>

          <div className="portal-benefits">
            <h3>Why Use Our Student Portal?</h3>
            <div className="benefits-list">
              <div className="benefit-item">
                <span className="benefit-icon">🔒</span>
                <div>
                  <h4>Secure & Private</h4>
                  <p>Your data is protected with bank-level security</p>
                </div>
              </div>
              
              <div className="benefit-item">
                <span className="benefit-icon">📱</span>
                <div>
                  <h4>Mobile Friendly</h4>
                  <p>Access your portal from any device, anywhere</p>
                </div>
              </div>
              
              <div className="benefit-item">
                <span className="benefit-icon">⚡</span>
                <div>
                  <h4>Real-time Updates</h4>
                  <p>Get instant notifications about your application</p>
                </div>
              </div>
              
              <div className="benefit-item">
                <span className="benefit-icon">🎯</span>
                <div>
                  <h4>Personalized Experience</h4>
                  <p>Customized dashboard based on your journey stage</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Login Modal */}
      {isLoginOpen && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Student Login</h3>
              <button className="close-modal" onClick={toggleLogin}>×</button>
            </div>
            
            <form onSubmit={handleLogin} className="login-form">
              <div className="form-group">
                <label htmlFor="loginEmail">Email Address</label>
                <input
                  type="email"
                  id="loginEmail"
                  name="email"
                  value={loginData.email}
                  onChange={handleLoginChange}
                  placeholder="Enter your email"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="loginPassword">Password</label>
                <div className="password-input">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="loginPassword"
                    name="password"
                    value={loginData.password}
                    onChange={handleLoginChange}
                    placeholder="Enter your password"
                    required
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>

              <button type="submit" className="btn-primary full-width">
                <LogIn size={20} />
                Login to Portal
              </button>

              <div className="form-footer">
                <a href="#" className="forgot-password">Forgot Password?</a>
                <p>
                  Don't have an account? 
                  <button type="button" onClick={toggleSignup} className="link-btn">
                    Register here
                  </button>
                </p>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Signup Modal */}
      {isSignupOpen && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Student Registration</h3>
              <button className="close-modal" onClick={toggleSignup}>×</button>
            </div>
            
            <form onSubmit={handleSignup} className="signup-form">
              <div className="form-group">
                <label htmlFor="signupName">Full Name</label>
                <input
                  type="text"
                  id="signupName"
                  name="name"
                  value={signupData.name}
                  onChange={handleSignupChange}
                  placeholder="Enter your full name"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="signupEmail">Email Address</label>
                <input
                  type="email"
                  id="signupEmail"
                  name="email"
                  value={signupData.email}
                  onChange={handleSignupChange}
                  placeholder="Enter your email"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="signupPhone">Phone Number</label>
                <input
                  type="tel"
                  id="signupPhone"
                  name="phone"
                  value={signupData.phone}
                  onChange={handleSignupChange}
                  placeholder="Enter your phone number"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="signupPassword">Password</label>
                <div className="password-input">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="signupPassword"
                    name="password"
                    value={signupData.password}
                    onChange={handleSignupChange}
                    placeholder="Create a password"
                    required
                  />
                  <button
                    type="button"
                    className="password-toggle"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="confirmPassword">Confirm Password</label>
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="confirmPassword"
                  name="confirmPassword"
                  value={signupData.confirmPassword}
                  onChange={handleSignupChange}
                  placeholder="Confirm your password"
                  required
                />
              </div>

              <button type="submit" className="btn-primary full-width">
                <UserPlus size={20} />
                Create Account
              </button>

              <div className="form-footer">
                <p>
                  Already have an account? 
                  <button type="button" onClick={toggleLogin} className="link-btn">
                    Login here
                  </button>
                </p>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  )
}

export default StudentPortal
