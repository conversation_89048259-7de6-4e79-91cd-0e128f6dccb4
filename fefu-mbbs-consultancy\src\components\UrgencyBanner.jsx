import React, { useState, useEffect } from 'react'
import { Clock, AlertTriangle, Gift } from 'lucide-react'

const UrgencyBanner = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    // Set deadline to 30 days from now
    const deadline = new Date()
    deadline.setDate(deadline.getDate() + 30)
    
    const timer = setInterval(() => {
      const now = new Date().getTime()
      const distance = deadline.getTime() - now

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  return (
    <div className="urgency-banner">
      <div className="container">
        <div className="urgency-content">
          <div className="urgency-icon">
            <AlertTriangle size={24} />
          </div>
          
          <div className="urgency-text">
            <h4>🔥 Limited Time Offer - FEFU MBBS Admission 2024</h4>
            <p>Get FREE visa processing worth ₹50,000 + Priority admission support</p>
          </div>

          <div className="countdown-timer">
            <div className="timer-item">
              <span className="timer-number">{timeLeft.days}</span>
              <span className="timer-label">Days</span>
            </div>
            <div className="timer-separator">:</div>
            <div className="timer-item">
              <span className="timer-number">{timeLeft.hours}</span>
              <span className="timer-label">Hours</span>
            </div>
            <div className="timer-separator">:</div>
            <div className="timer-item">
              <span className="timer-number">{timeLeft.minutes}</span>
              <span className="timer-label">Min</span>
            </div>
            <div className="timer-separator">:</div>
            <div className="timer-item">
              <span className="timer-number">{timeLeft.seconds}</span>
              <span className="timer-label">Sec</span>
            </div>
          </div>

          <button 
            className="urgency-cta"
            onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}
          >
            <Gift size={16} />
            Claim Offer Now
          </button>
        </div>
      </div>
    </div>
  )
}

export default UrgencyBanner
