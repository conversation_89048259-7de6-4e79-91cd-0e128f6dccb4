{"name": "fefu-mbbs-consultancy", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"framer-motion": "^12.23.3", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-countup": "^6.5.3", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-modal": "^3.16.3"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}