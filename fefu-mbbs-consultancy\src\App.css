/* Header Styles */
.header {
  background-color: var(--background-white);
  box-shadow: var(--shadow-light);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) 0;
}

.logo h2 {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  margin: 0;
}

@media (min-width: 768px) {
  .logo h2 {
    font-size: var(--font-size-xl);
  }
}

/* Desktop Navigation */
.desktop-nav {
  display: none;
}

.desktop-nav ul {
  display: flex;
  list-style: none;
  gap: var(--spacing-6);
}

.desktop-nav button {
  background: none;
  border: none;
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  padding: var(--spacing-2) 0;
  transition: color 0.2s ease;
}

.desktop-nav button:hover {
  color: var(--primary-color);
}

@media (min-width: 1024px) {
  .desktop-nav {
    display: block;
  }
}

/* Header Contact */
.header-contact {
  display: none;
  gap: var(--spacing-4);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

@media (min-width: 1280px) {
  .header-contact {
    display: flex;
  }
}

/* Mobile Menu */
.mobile-menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--spacing-2);
  min-height: 44px;
  min-width: 44px;
}

@media (min-width: 1024px) {
  .mobile-menu-btn {
    display: none;
  }
}

.mobile-nav {
  background-color: var(--background-white);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-4) 0;
}

.mobile-nav ul {
  list-style: none;
  margin-bottom: var(--spacing-4);
}

.mobile-nav li {
  margin-bottom: var(--spacing-2);
}

.mobile-nav button {
  background: none;
  border: none;
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  padding: var(--spacing-3) 0;
  width: 100%;
  text-align: left;
  font-size: var(--font-size-lg);
  min-height: 44px;
}

.mobile-nav button:hover {
  color: var(--primary-color);
}

.mobile-contact {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%);
  padding: calc(80px + var(--spacing-8)) 0 var(--spacing-16) 0;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  display: grid;
  gap: var(--spacing-8);
  align-items: center;
}

@media (min-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-12);
  }
}

.hero-text {
  text-align: center;
}

@media (min-width: 1024px) {
  .hero-text {
    text-align: left;
  }
}

.hero h1 {
  margin-bottom: var(--spacing-6);
  line-height: 1.1;
}

.highlight {
  color: var(--primary-color);
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-8);
  color: var(--text-secondary);
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

@media (min-width: 1024px) {
  .stat {
    flex-direction: row;
    text-align: left;
  }
}

.stat-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-2);
}

@media (min-width: 1024px) {
  .stat-icon {
    margin-bottom: 0;
    margin-right: var(--spacing-3);
  }
}

.stat-content h3 {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-1);
}

.stat-content p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  align-items: center;
}

@media (min-width: 640px) {
  .hero-buttons {
    flex-direction: row;
    justify-content: center;
  }
}

@media (min-width: 1024px) {
  .hero-buttons {
    justify-content: flex-start;
  }
}

.hero-image {
  display: flex;
  justify-content: center;
}

.hero-card {
  background-color: var(--background-white);
  padding: var(--spacing-6);
  border-radius: 12px;
  box-shadow: var(--shadow-large);
  max-width: 400px;
  width: 100%;
}

.hero-card h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
  text-align: center;
}

.hero-card ul {
  list-style: none;
}

.hero-card li {
  padding: var(--spacing-2) 0;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
}

.hero-card li:last-child {
  border-bottom: none;
}

/* About FEFU Section */
.about-fefu {
  padding: var(--spacing-16) 0;
  background-color: var(--background-white);
}

.about-content {
  display: grid;
  gap: var(--spacing-12);
}

@media (min-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
  }
}

.university-info {
  display: grid;
  gap: var(--spacing-6);
}

.info-item {
  display: flex;
  gap: var(--spacing-4);
  align-items: flex-start;
}

.info-icon {
  color: var(--primary-color);
  flex-shrink: 0;
  margin-top: var(--spacing-1);
}

.info-item h4 {
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
}

.info-item p {
  margin: 0;
  color: var(--text-secondary);
}

.about-description {
  margin-top: var(--spacing-8);
}

.about-description h3 {
  margin-bottom: var(--spacing-4);
  color: var(--primary-color);
}

.about-features h3 {
  margin-bottom: var(--spacing-6);
  color: var(--primary-color);
  text-align: center;
}

@media (min-width: 1024px) {
  .about-features h3 {
    text-align: left;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.feature-card {
  background-color: var(--background-light);
  padding: var(--spacing-4);
  border-radius: 8px;
  text-align: center;
}

.feature-card h4 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-2);
}

.feature-card p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Services Section */
.services {
  padding: var(--spacing-16) 0;
  background-color: var(--background-light);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-16);
}

.service-card {
  background-color: var(--background-white);
  padding: var(--spacing-6);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
}

.service-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
  display: flex;
  justify-content: center;
}

.service-card h3 {
  margin-bottom: var(--spacing-3);
  color: var(--text-primary);
}

.service-card p {
  margin: 0;
  color: var(--text-secondary);
}

/* Process Section */
.process-section {
  background-color: var(--background-white);
  padding: var(--spacing-8);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
}

.process-section h3 {
  text-align: center;
  margin-bottom: var(--spacing-8);
  color: var(--primary-color);
}

.process-steps {
  display: grid;
  gap: var(--spacing-6);
}

@media (min-width: 768px) {
  .process-steps {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .process-steps {
    grid-template-columns: repeat(4, 1fr);
  }
}

.step {
  text-align: center;
}

.step-number {
  width: 48px;
  height: 48px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: var(--font-size-lg);
  margin: 0 auto var(--spacing-4) auto;
}

.step-content h4 {
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
}

.step-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Contact Section */
.contact {
  padding: var(--spacing-16) 0;
  background-color: var(--background-white);
}

.contact-content {
  display: grid;
  gap: var(--spacing-12);
}

@media (min-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
  }
}

.contact-info h3 {
  margin-bottom: var(--spacing-4);
  color: var(--primary-color);
}

.contact-info p {
  margin-bottom: var(--spacing-6);
  color: var(--text-secondary);
}

.contact-details {
  display: grid;
  gap: var(--spacing-6);
}

.contact-item {
  display: flex;
  gap: var(--spacing-4);
  align-items: flex-start;
}

.contact-icon {
  color: var(--primary-color);
  flex-shrink: 0;
  margin-top: var(--spacing-1);
}

.contact-item h4 {
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
}

.contact-item p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Contact Form */
.contact-form-container {
  background-color: var(--background-light);
  padding: var(--spacing-8);
  border-radius: 12px;
}

.success-message {
  background-color: var(--accent-color);
  color: white;
  padding: var(--spacing-4);
  border-radius: 8px;
  margin-bottom: var(--spacing-6);
  text-align: center;
}

.success-message h4 {
  margin-bottom: var(--spacing-2);
  color: white;
}

.success-message p {
  margin: 0;
  color: white;
  opacity: 0.9;
}

.contact-form {
  display: grid;
  gap: var(--spacing-4);
}

.form-row {
  display: grid;
  gap: var(--spacing-4);
}

@media (min-width: 640px) {
  .form-row {
    grid-template-columns: 1fr 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: var(--spacing-2);
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: var(--spacing-3);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: var(--font-size-base);
  font-family: inherit;
  transition: border-color 0.2s ease;
  min-height: 44px; /* Touch-friendly */
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
}

.submit-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: var(--spacing-4) var(--spacing-6);
  border-radius: 8px;
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  min-height: 48px;
}

.submit-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

/* Footer */
.footer {
  background-color: var(--text-primary);
  color: white;
  padding: var(--spacing-12) 0 var(--spacing-6) 0;
}

.footer-content {
  display: grid;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

@media (min-width: 640px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .footer-content {
    grid-template-columns: 2fr 1fr 1fr 1fr;
  }
}

.footer-section h3,
.footer-section h4 {
  color: white;
  margin-bottom: var(--spacing-4);
}

.footer-section p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-4);
}

.social-links {
  display: flex;
  gap: var(--spacing-3);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.social-links a:hover {
  background-color: var(--primary-color);
}

.footer-section ul {
  list-style: none;
}

.footer-section li {
  margin-bottom: var(--spacing-2);
}

.footer-section button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 0;
  font-size: var(--font-size-base);
  transition: color 0.2s ease;
}

.footer-section button:hover {
  color: white;
}

.footer-contact .contact-item {
  margin-bottom: var(--spacing-3);
  color: rgba(255, 255, 255, 0.8);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--spacing-6);
}

.footer-bottom-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  align-items: center;
  text-align: center;
}

@media (min-width: 768px) {
  .footer-bottom-content {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

.footer-bottom p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
}

.footer-links {
  display: flex;
  gap: var(--spacing-4);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color 0.2s ease;
}

.footer-links a:hover {
  color: white;
}

/* Urgency Banner */
.urgency-banner {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: var(--spacing-3) 0;
  position: relative;
  overflow: hidden;
}

.urgency-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.urgency-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .urgency-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-3);
  }
}

.urgency-icon {
  color: #fbbf24;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.urgency-text h4 {
  margin: 0 0 var(--spacing-1) 0;
  color: white;
  font-size: var(--font-size-lg);
}

.urgency-text p {
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--font-size-sm);
}

.countdown-timer {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  background: rgba(0, 0, 0, 0.2);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: 8px;
}

.timer-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 40px;
}

.timer-number {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: #fbbf24;
}

.timer-label {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.8);
}

.timer-separator {
  font-size: var(--font-size-lg);
  font-weight: 700;
  color: #fbbf24;
}

.urgency-cta {
  background: #fbbf24;
  color: #1f2937;
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  transition: all 0.2s ease;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

.urgency-cta:hover {
  background: #f59e0b;
  transform: translateY(-2px);
}

/* Trust Signals */
.trust-signals {
  padding: var(--spacing-12) 0;
  background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%);
}

.trust-content {
  text-align: center;
}

.trust-header {
  margin-bottom: var(--spacing-10);
}

.trust-icon {
  color: var(--accent-color);
  margin-bottom: var(--spacing-4);
}

.trust-header h3 {
  margin-bottom: var(--spacing-3);
  color: var(--text-primary);
}

.trust-header p {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-10);
}

.achievement-card {
  background: var(--background-white);
  padding: var(--spacing-6);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.achievement-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
}

.achievement-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
  display: flex;
  justify-content: center;
}

.achievement-card h4 {
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
}

.achievement-card p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.certifications {
  background: var(--background-white);
  padding: var(--spacing-6);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
  margin-bottom: var(--spacing-8);
}

.certifications h4 {
  margin-bottom: var(--spacing-4);
  color: var(--primary-color);
}

.cert-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-3);
}

.cert-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--text-secondary);
}

.cert-item svg {
  color: var(--accent-color);
  flex-shrink: 0;
}

.recent-activity {
  background: var(--background-white);
  padding: var(--spacing-6);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
}

.recent-activity h4 {
  margin-bottom: var(--spacing-4);
  color: var(--primary-color);
}

.activity-feed {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  color: var(--text-secondary);
}

.activity-dot {
  width: 8px;
  height: 8px;
  background: var(--accent-color);
  border-radius: 50%;
  flex-shrink: 0;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* NEET Evaluator */
.neet-evaluator {
  padding: var(--spacing-16) 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
}

.evaluator-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.evaluator-header {
  margin-bottom: var(--spacing-10);
}

.evaluator-icon {
  color: var(--secondary-color);
  margin-bottom: var(--spacing-4);
}

.evaluator-header h3 {
  color: white;
  margin-bottom: var(--spacing-3);
}

.evaluator-header p {
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--font-size-lg);
}

.evaluator-form {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-8);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.evaluator-form .form-group {
  text-align: left;
}

.evaluator-form .form-group label {
  color: white;
  margin-bottom: var(--spacing-2);
  font-weight: 600;
}

.evaluator-form .form-group input,
.evaluator-form .form-group select {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid transparent;
  color: var(--text-primary);
}

.evaluator-form .form-group input:focus,
.evaluator-form .form-group select:focus {
  background: white;
  border-color: var(--secondary-color);
}

.evaluate-btn {
  background: var(--secondary-color);
  color: var(--text-primary);
  border: none;
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: 12px;
  font-size: var(--font-size-lg);
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin: 0 auto;
  transition: all 0.2s ease;
  min-height: 56px;
}

.evaluate-btn:hover:not(:disabled) {
  background: #f59e0b;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.evaluate-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.evaluation-result {
  animation: slideIn 0.5s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-card {
  background: white;
  color: var(--text-primary);
  padding: var(--spacing-8);
  border-radius: 16px;
  box-shadow: var(--shadow-large);
  text-align: left;
}

.result-card.green {
  border-left: 6px solid var(--accent-color);
}

.result-card.orange {
  border-left: 6px solid var(--secondary-color);
}

.result-card.yellow {
  border-left: 6px solid #eab308;
}

.result-card.red {
  border-left: 6px solid #ef4444;
}

.result-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.result-header svg {
  color: var(--accent-color);
}

.result-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.eligibility-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background: var(--background-light);
  border-radius: 8px;
}

.status-label {
  font-weight: 600;
  color: var(--text-primary);
}

.status-value {
  font-weight: 700;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: 6px;
  font-size: var(--font-size-sm);
}

.status-value.green {
  background: var(--accent-color);
  color: white;
}

.status-value.orange {
  background: var(--secondary-color);
  color: white;
}

.status-value.yellow {
  background: #eab308;
  color: white;
}

.status-value.red {
  background: #ef4444;
  color: white;
}

.recommendation {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-6);
  color: var(--text-secondary);
}

.next-steps h5 {
  margin-bottom: var(--spacing-3);
  color: var(--primary-color);
}

.next-steps ul {
  list-style: none;
  padding: 0;
}

.next-steps li {
  padding: var(--spacing-2) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--spacing-6);
}

.next-steps li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-weight: 700;
}

.result-actions {
  display: flex;
  gap: var(--spacing-4);
  margin-top: var(--spacing-6);
  flex-wrap: wrap;
}

@media (max-width: 640px) {
  .result-actions {
    flex-direction: column;
  }
}

.evaluator-disclaimer {
  margin-top: var(--spacing-6);
  text-align: center;
}

.evaluator-disclaimer p {
  color: rgba(255, 255, 255, 0.7);
  font-size: var(--font-size-sm);
  margin: 0;
}

/* Testimonials */
.testimonials {
  padding: var(--spacing-16) 0;
  background: var(--background-white);
}

.testimonial-showcase {
  display: grid;
  gap: var(--spacing-12);
  margin-bottom: var(--spacing-12);
}

@media (min-width: 1024px) {
  .testimonial-showcase {
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-16);
  }
}

.testimonial-main {
  position: relative;
}

.testimonial-card {
  background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%);
  padding: var(--spacing-8);
  border-radius: 20px;
  box-shadow: var(--shadow-large);
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.quote-icon {
  color: var(--primary-color);
  opacity: 0.3;
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
}

.testimonial-content {
  position: relative;
  z-index: 1;
}

.rating {
  display: flex;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-4);
  color: var(--secondary-color);
}

.testimonial-text {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  color: var(--text-primary);
  margin-bottom: var(--spacing-6);
  font-style: italic;
}

.student-info {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
}

.student-avatar {
  position: relative;
  flex-shrink: 0;
}

.student-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-color);
}

.video-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.video-overlay:hover {
  background: var(--primary-dark);
  transform: translate(-50%, -50%) scale(1.1);
}

.student-details h4 {
  margin-bottom: var(--spacing-1);
  color: var(--text-primary);
}

.course {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: var(--spacing-2);
}

.location {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
}

.achievement {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.neet-score {
  background: var(--accent-color);
  color: white;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: 4px;
  font-size: var(--font-size-xs);
  font-weight: 600;
  width: fit-content;
}

.achievement-text {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
}

.testimonial-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-4);
  margin-top: var(--spacing-6);
}

.control-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

.testimonial-indicators {
  display: flex;
  gap: var(--spacing-2);
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.indicator.active {
  background: var(--primary-color);
  transform: scale(1.2);
}

.testimonial-stats {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: var(--spacing-8);
  border-radius: 20px;
  height: fit-content;
}

.testimonial-stats h3 {
  color: white;
  margin-bottom: var(--spacing-6);
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-4);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.9);
}

.recent-admissions h4 {
  color: white;
  margin-bottom: var(--spacing-4);
}

.admission-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.admission-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--font-size-sm);
}

.admission-dot {
  width: 8px;
  height: 8px;
  background: var(--secondary-color);
  border-radius: 50%;
  flex-shrink: 0;
  animation: pulse-dot 2s infinite;
}

.testimonial-cta {
  text-align: center;
  background: var(--background-light);
  padding: var(--spacing-8);
  border-radius: 16px;
}

.testimonial-cta h3 {
  margin-bottom: var(--spacing-3);
  color: var(--text-primary);
}

.testimonial-cta p {
  margin-bottom: var(--spacing-6);
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
}

/* Cost Calculator */
.cost-calculator {
  padding: var(--spacing-16) 0;
  background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%);
}

.calculator-content {
  max-width: 900px;
  margin: 0 auto;
}

.calculator-header {
  text-align: center;
  margin-bottom: var(--spacing-10);
}

.calculator-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
}

.calculator-header h3 {
  margin-bottom: var(--spacing-3);
  color: var(--text-primary);
}

.calculator-header p {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
}

.calculator-form {
  background: var(--background-white);
  padding: var(--spacing-8);
  border-radius: 16px;
  box-shadow: var(--shadow-medium);
  margin-bottom: var(--spacing-8);
}

.calculator-form .form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.calculate-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: 12px;
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  margin: 0 auto;
  transition: all 0.2s ease;
  min-height: 56px;
}

.calculate-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.calculation-result {
  background: var(--background-white);
  padding: var(--spacing-8);
  border-radius: 16px;
  box-shadow: var(--shadow-medium);
  animation: slideIn 0.5s ease;
}

.calculation-result h4 {
  text-align: center;
  margin-bottom: var(--spacing-6);
  color: var(--primary-color);
}

.cost-breakdown {
  display: grid;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.cost-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background: var(--background-light);
  border-radius: 12px;
  transition: transform 0.2s ease;
}

.cost-item:hover {
  transform: translateX(4px);
}

.cost-icon {
  color: var(--primary-color);
  background: rgba(37, 99, 235, 0.1);
  padding: var(--spacing-3);
  border-radius: 50%;
  flex-shrink: 0;
}

.cost-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cost-label {
  color: var(--text-primary);
  font-weight: 500;
}

.cost-amount {
  color: var(--primary-color);
  font-weight: 700;
  font-size: var(--font-size-lg);
}

.total-cost {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  padding: var(--spacing-6);
  border-radius: 12px;
  text-align: center;
  margin-bottom: var(--spacing-6);
}

.total-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.total-label {
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.total-value {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--secondary-color);
}

.monthly-average {
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--font-size-sm);
}

.cost-benefits {
  margin-bottom: var(--spacing-6);
}

.cost-benefits h5 {
  margin-bottom: var(--spacing-3);
  color: var(--primary-color);
}

.cost-benefits ul {
  list-style: none;
  padding: 0;
}

.cost-benefits li {
  padding: var(--spacing-2) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--spacing-6);
}

.cost-benefits li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-weight: 700;
}

.calculator-cta {
  text-align: center;
}

/* Student Journey */
.student-journey {
  padding: var(--spacing-16) 0;
  background: var(--background-white);
}

.journey-content {
  display: grid;
  gap: var(--spacing-12);
}

@media (min-width: 1024px) {
  .journey-content {
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-16);
  }
}

.journey-timeline {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.timeline-step {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: var(--spacing-4);
  align-items: flex-start;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: var(--spacing-4);
  border-radius: 12px;
}

.timeline-step:hover {
  background: var(--background-light);
}

.timeline-step.active {
  background: var(--background-light);
  box-shadow: var(--shadow-light);
}

.step-connector {
  position: relative;
  width: 2px;
  min-height: 100px;
}

.connector-line {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 60px;
  background: var(--border-color);
}

.step-marker {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
}

.step-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.2s ease;
}

.step-status {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.pending-dot {
  width: 8px;
  height: 8px;
  background: var(--border-color);
  border-radius: 50%;
}

.step-content {
  flex: 1;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.step-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.step-duration {
  background: var(--primary-color);
  color: white;
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: 20px;
  font-size: var(--font-size-xs);
  font-weight: 600;
}

.step-description {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-3);
}

.step-details {
  background: var(--background-white);
  padding: var(--spacing-4);
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
  animation: slideIn 0.3s ease;
}

.step-details h5 {
  margin-bottom: var(--spacing-3);
  color: var(--primary-color);
}

.step-details ul {
  list-style: none;
  padding: 0;
}

.step-details li {
  padding: var(--spacing-1) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--spacing-5);
}

.step-details li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: 700;
}

.step-arrow {
  transition: all 0.2s ease;
}

.journey-summary {
  height: fit-content;
}

.summary-card {
  background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%);
  padding: var(--spacing-8);
  border-radius: 16px;
  box-shadow: var(--shadow-medium);
}

.summary-card h3 {
  margin-bottom: var(--spacing-6);
  color: var(--primary-color);
  text-align: center;
}

.highlight-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.highlight-stats .stat {
  text-align: center;
  padding: var(--spacing-4);
  background: var(--background-white);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
}

.highlight-stats .stat-number {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-1);
}

.highlight-stats .stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.success-metrics {
  margin-bottom: var(--spacing-8);
}

.success-metrics h4 {
  margin-bottom: var(--spacing-4);
  color: var(--primary-color);
}

.metrics-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.metric-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--text-secondary);
}

.metric-item svg {
  color: var(--accent-color);
  flex-shrink: 0;
}

.journey-cta {
  text-align: center;
  background: var(--background-white);
  padding: var(--spacing-6);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
}

.journey-cta h4 {
  margin-bottom: var(--spacing-2);
  color: var(--primary-color);
}

.journey-cta p {
  margin-bottom: var(--spacing-4);
  color: var(--text-secondary);
}

.journey-testimonial {
  margin-top: var(--spacing-12);
  background: var(--background-light);
  padding: var(--spacing-8);
  border-radius: 16px;
}

.testimonial-content {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.testimonial-quote h4 {
  font-style: italic;
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
}

.testimonial-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-3);
}

.testimonial-author img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.testimonial-author div {
  text-align: left;
}

.testimonial-author strong {
  display: block;
  color: var(--text-primary);
}

.testimonial-author span {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}
