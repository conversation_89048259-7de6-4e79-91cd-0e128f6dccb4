/* Header Styles */
.header {
  background-color: var(--background-white);
  box-shadow: var(--shadow-light);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) 0;
}

.logo h2 {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  margin: 0;
}

@media (min-width: 768px) {
  .logo h2 {
    font-size: var(--font-size-xl);
  }
}

/* Desktop Navigation */
.desktop-nav {
  display: none;
}

.desktop-nav ul {
  display: flex;
  list-style: none;
  gap: var(--spacing-6);
}

.desktop-nav button {
  background: none;
  border: none;
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  padding: var(--spacing-2) 0;
  transition: color 0.2s ease;
}

.desktop-nav button:hover {
  color: var(--primary-color);
}

@media (min-width: 1024px) {
  .desktop-nav {
    display: block;
  }
}

/* Header Contact */
.header-contact {
  display: none;
  gap: var(--spacing-4);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

@media (min-width: 1280px) {
  .header-contact {
    display: flex;
  }
}

/* Mobile Menu */
.mobile-menu-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--spacing-2);
  min-height: 44px;
  min-width: 44px;
}

@media (min-width: 1024px) {
  .mobile-menu-btn {
    display: none;
  }
}

.mobile-nav {
  background-color: var(--background-white);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-4) 0;
}

.mobile-nav ul {
  list-style: none;
  margin-bottom: var(--spacing-4);
}

.mobile-nav li {
  margin-bottom: var(--spacing-2);
}

.mobile-nav button {
  background: none;
  border: none;
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  padding: var(--spacing-3) 0;
  width: 100%;
  text-align: left;
  font-size: var(--font-size-lg);
  min-height: 44px;
}

.mobile-nav button:hover {
  color: var(--primary-color);
}

.mobile-contact {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--background-light) 0%, var(--background-white) 100%);
  padding: calc(80px + var(--spacing-8)) 0 var(--spacing-16) 0;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  display: grid;
  gap: var(--spacing-8);
  align-items: center;
}

@media (min-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-12);
  }
}

.hero-text {
  text-align: center;
}

@media (min-width: 1024px) {
  .hero-text {
    text-align: left;
  }
}

.hero h1 {
  margin-bottom: var(--spacing-6);
  line-height: 1.1;
}

.highlight {
  color: var(--primary-color);
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-8);
  color: var(--text-secondary);
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

@media (min-width: 1024px) {
  .stat {
    flex-direction: row;
    text-align: left;
  }
}

.stat-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-2);
}

@media (min-width: 1024px) {
  .stat-icon {
    margin-bottom: 0;
    margin-right: var(--spacing-3);
  }
}

.stat-content h3 {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-1);
}

.stat-content p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  align-items: center;
}

@media (min-width: 640px) {
  .hero-buttons {
    flex-direction: row;
    justify-content: center;
  }
}

@media (min-width: 1024px) {
  .hero-buttons {
    justify-content: flex-start;
  }
}

.hero-image {
  display: flex;
  justify-content: center;
}

.hero-card {
  background-color: var(--background-white);
  padding: var(--spacing-6);
  border-radius: 12px;
  box-shadow: var(--shadow-large);
  max-width: 400px;
  width: 100%;
}

.hero-card h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
  text-align: center;
}

.hero-card ul {
  list-style: none;
}

.hero-card li {
  padding: var(--spacing-2) 0;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
}

.hero-card li:last-child {
  border-bottom: none;
}

/* About FEFU Section */
.about-fefu {
  padding: var(--spacing-16) 0;
  background-color: var(--background-white);
}

.about-content {
  display: grid;
  gap: var(--spacing-12);
}

@media (min-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
  }
}

.university-info {
  display: grid;
  gap: var(--spacing-6);
}

.info-item {
  display: flex;
  gap: var(--spacing-4);
  align-items: flex-start;
}

.info-icon {
  color: var(--primary-color);
  flex-shrink: 0;
  margin-top: var(--spacing-1);
}

.info-item h4 {
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
}

.info-item p {
  margin: 0;
  color: var(--text-secondary);
}

.about-description {
  margin-top: var(--spacing-8);
}

.about-description h3 {
  margin-bottom: var(--spacing-4);
  color: var(--primary-color);
}

.about-features h3 {
  margin-bottom: var(--spacing-6);
  color: var(--primary-color);
  text-align: center;
}

@media (min-width: 1024px) {
  .about-features h3 {
    text-align: left;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.feature-card {
  background-color: var(--background-light);
  padding: var(--spacing-4);
  border-radius: 8px;
  text-align: center;
}

.feature-card h4 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-2);
}

.feature-card p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Services Section */
.services {
  padding: var(--spacing-16) 0;
  background-color: var(--background-light);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-16);
}

.service-card {
  background-color: var(--background-white);
  padding: var(--spacing-6);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
}

.service-icon {
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
  display: flex;
  justify-content: center;
}

.service-card h3 {
  margin-bottom: var(--spacing-3);
  color: var(--text-primary);
}

.service-card p {
  margin: 0;
  color: var(--text-secondary);
}

/* Process Section */
.process-section {
  background-color: var(--background-white);
  padding: var(--spacing-8);
  border-radius: 12px;
  box-shadow: var(--shadow-light);
}

.process-section h3 {
  text-align: center;
  margin-bottom: var(--spacing-8);
  color: var(--primary-color);
}

.process-steps {
  display: grid;
  gap: var(--spacing-6);
}

@media (min-width: 768px) {
  .process-steps {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .process-steps {
    grid-template-columns: repeat(4, 1fr);
  }
}

.step {
  text-align: center;
}

.step-number {
  width: 48px;
  height: 48px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: var(--font-size-lg);
  margin: 0 auto var(--spacing-4) auto;
}

.step-content h4 {
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
}

.step-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Contact Section */
.contact {
  padding: var(--spacing-16) 0;
  background-color: var(--background-white);
}

.contact-content {
  display: grid;
  gap: var(--spacing-12);
}

@media (min-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
  }
}

.contact-info h3 {
  margin-bottom: var(--spacing-4);
  color: var(--primary-color);
}

.contact-info p {
  margin-bottom: var(--spacing-6);
  color: var(--text-secondary);
}

.contact-details {
  display: grid;
  gap: var(--spacing-6);
}

.contact-item {
  display: flex;
  gap: var(--spacing-4);
  align-items: flex-start;
}

.contact-icon {
  color: var(--primary-color);
  flex-shrink: 0;
  margin-top: var(--spacing-1);
}

.contact-item h4 {
  margin-bottom: var(--spacing-2);
  color: var(--text-primary);
}

.contact-item p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Contact Form */
.contact-form-container {
  background-color: var(--background-light);
  padding: var(--spacing-8);
  border-radius: 12px;
}

.success-message {
  background-color: var(--accent-color);
  color: white;
  padding: var(--spacing-4);
  border-radius: 8px;
  margin-bottom: var(--spacing-6);
  text-align: center;
}

.success-message h4 {
  margin-bottom: var(--spacing-2);
  color: white;
}

.success-message p {
  margin: 0;
  color: white;
  opacity: 0.9;
}

.contact-form {
  display: grid;
  gap: var(--spacing-4);
}

.form-row {
  display: grid;
  gap: var(--spacing-4);
}

@media (min-width: 640px) {
  .form-row {
    grid-template-columns: 1fr 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: var(--spacing-2);
  font-weight: 600;
  color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: var(--spacing-3);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: var(--font-size-base);
  font-family: inherit;
  transition: border-color 0.2s ease;
  min-height: 44px; /* Touch-friendly */
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-group input.error,
.form-group select.error,
.form-group textarea.error {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-1);
}

.submit-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: var(--spacing-4) var(--spacing-6);
  border-radius: 8px;
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  min-height: 48px;
}

.submit-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

/* Footer */
.footer {
  background-color: var(--text-primary);
  color: white;
  padding: var(--spacing-12) 0 var(--spacing-6) 0;
}

.footer-content {
  display: grid;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

@media (min-width: 640px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .footer-content {
    grid-template-columns: 2fr 1fr 1fr 1fr;
  }
}

.footer-section h3,
.footer-section h4 {
  color: white;
  margin-bottom: var(--spacing-4);
}

.footer-section p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-4);
}

.social-links {
  display: flex;
  gap: var(--spacing-3);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.social-links a:hover {
  background-color: var(--primary-color);
}

.footer-section ul {
  list-style: none;
}

.footer-section li {
  margin-bottom: var(--spacing-2);
}

.footer-section button {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 0;
  font-size: var(--font-size-base);
  transition: color 0.2s ease;
}

.footer-section button:hover {
  color: white;
}

.footer-contact .contact-item {
  margin-bottom: var(--spacing-3);
  color: rgba(255, 255, 255, 0.8);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--spacing-6);
}

.footer-bottom-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  align-items: center;
  text-align: center;
}

@media (min-width: 768px) {
  .footer-bottom-content {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
}

.footer-bottom p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
}

.footer-links {
  display: flex;
  gap: var(--spacing-4);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: var(--font-size-sm);
  transition: color 0.2s ease;
}

.footer-links a:hover {
  color: white;
}
