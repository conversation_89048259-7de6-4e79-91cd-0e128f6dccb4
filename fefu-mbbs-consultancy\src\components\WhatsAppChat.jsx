import React, { useState } from 'react'
import { MessageCircle, X, Send, Phone, Clock, User } from 'lucide-react'

const WhatsAppChat = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [message, setMessage] = useState('')

  const whatsappNumber = "+919876543210"
  const predefinedMessages = [
    "Hi! I want to know about FEFU MBBS admission",
    "What are the fees for MBBS at FEFU?",
    "Can you help me with the admission process?",
    "I need information about visa processing",
    "What documents are required for admission?"
  ]

  const consultants = [
    {
      name: "Dr. <PERSON><PERSON>",
      role: "Senior Consultant",
      status: "online",
      avatar: "/api/placeholder/40/40"
    },
    {
      name: "<PERSON><PERSON>",
      role: "Admission Counselor",
      status: "online",
      avatar: "/api/placeholder/40/40"
    }
  ]

  const toggleChat = () => {
    setIsOpen(!isOpen)
  }

  const sendWhatsAppMessage = (customMessage = null) => {
    const messageText = customMessage || message || "Hi! I'm interested in FEFU MBBS admission. Can you please provide more information?"
    const encodedMessage = encodeURIComponent(messageText)
    const whatsappUrl = `https://wa.me/${whatsappNumber.replace('+', '')}?text=${encodedMessage}`
    window.open(whatsappUrl, '_blank')
    
    if (!customMessage) {
      setMessage('')
    }
    setIsOpen(false)
  }

  const callDirectly = () => {
    window.open(`tel:${whatsappNumber}`, '_self')
  }

  return (
    <>
      {/* WhatsApp Floating Button */}
      <div className="whatsapp-float">
        <button className="whatsapp-btn" onClick={toggleChat}>
          <MessageCircle size={24} />
          <span className="whatsapp-pulse"></span>
        </button>
        
        {!isOpen && (
          <div className="whatsapp-tooltip">
            <span>Need help? Chat with us!</span>
          </div>
        )}
      </div>

      {/* WhatsApp Chat Window */}
      {isOpen && (
        <div className="whatsapp-chat">
          <div className="chat-header">
            <div className="header-info">
              <div className="company-avatar">
                <MessageCircle size={24} />
              </div>
              <div className="company-details">
                <h4>FEFU MBBS Consultancy</h4>
                <p>
                  <span className="online-dot"></span>
                  Typically replies instantly
                </p>
              </div>
            </div>
            <button className="close-chat" onClick={toggleChat}>
              <X size={20} />
            </button>
          </div>

          <div className="chat-body">
            <div className="welcome-message">
              <div className="message-bubble">
                <p>👋 Hi there! Welcome to FEFU MBBS Consultancy!</p>
                <p>How can we help you with your MBBS journey today?</p>
                <span className="message-time">Just now</span>
              </div>
            </div>

            <div className="consultants-online">
              <h5>Our consultants are online:</h5>
              {consultants.map((consultant, index) => (
                <div key={index} className="consultant-item">
                  <img src={consultant.avatar} alt={consultant.name} />
                  <div className="consultant-info">
                    <span className="consultant-name">{consultant.name}</span>
                    <span className="consultant-role">{consultant.role}</span>
                  </div>
                  <div className="status-indicator online"></div>
                </div>
              ))}
            </div>

            <div className="quick-actions">
              <h5>Quick Actions:</h5>
              <div className="action-buttons">
                <button 
                  className="action-btn call-btn"
                  onClick={callDirectly}
                >
                  <Phone size={16} />
                  Call Now
                </button>
                <button 
                  className="action-btn schedule-btn"
                  onClick={() => {
                    sendWhatsAppMessage("I would like to schedule a free consultation")
                  }}
                >
                  <Clock size={16} />
                  Schedule Call
                </button>
              </div>
            </div>

            <div className="quick-messages">
              <h5>Quick Messages:</h5>
              {predefinedMessages.map((msg, index) => (
                <button
                  key={index}
                  className="quick-message-btn"
                  onClick={() => sendWhatsAppMessage(msg)}
                >
                  {msg}
                </button>
              ))}
            </div>
          </div>

          <div className="chat-footer">
            <div className="message-input">
              <input
                type="text"
                placeholder="Type your message..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && sendWhatsAppMessage()}
              />
              <button 
                className="send-btn"
                onClick={() => sendWhatsAppMessage()}
              >
                <Send size={18} />
              </button>
            </div>
            
            <div className="chat-footer-info">
              <p>🔒 Your privacy is protected. We'll respond via WhatsApp.</p>
            </div>
          </div>
        </div>
      )}

      {/* WhatsApp Sticky Banner */}
      <div className="whatsapp-banner">
        <div className="banner-content">
          <div className="banner-icon">
            <MessageCircle size={20} />
          </div>
          <div className="banner-text">
            <span>Get instant answers on WhatsApp!</span>
            <small>Our experts are online now</small>
          </div>
          <button 
            className="banner-btn"
            onClick={() => sendWhatsAppMessage("Hi! I need information about FEFU MBBS admission")}
          >
            Chat Now
          </button>
        </div>
      </div>
    </>
  )
}

export default WhatsAppChat
