import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { Send, Phone, Mail, MapPin, Clock } from 'lucide-react'

const ContactForm = () => {
  const [isSubmitted, setIsSubmitted] = useState(false)
  const { register, handleSubmit, formState: { errors }, reset } = useForm()

  const onSubmit = (data) => {
    console.log('Form submitted:', data)
    // Here you would typically send the data to your backend
    setIsSubmitted(true)
    reset()
    
    // Reset the success message after 5 seconds
    setTimeout(() => {
      setIsSubmitted(false)
    }, 5000)
  }

  return (
    <section id="contact" className="contact">
      <div className="container">
        <div className="section-header">
          <h2>Get Free Consultation</h2>
          <p>Take the first step towards your MBBS dream at FEFU</p>
        </div>

        <div className="contact-content">
          <div className="contact-info">
            <h3>Contact Information</h3>
            <p>Ready to start your medical education journey? Get in touch with our expert consultants.</p>
            
            <div className="contact-details">
              <div className="contact-item">
                <Phone className="contact-icon" size={20} />
                <div>
                  <h4>Phone</h4>
                  <p>+91 **********</p>
                  <p>+91 **********</p>
                </div>
              </div>
              
              <div className="contact-item">
                <Mail className="contact-icon" size={20} />
                <div>
                  <h4>Email</h4>
                  <p><EMAIL></p>
                  <p><EMAIL></p>
                </div>
              </div>
              
              <div className="contact-item">
                <MapPin className="contact-icon" size={20} />
                <div>
                  <h4>Office Address</h4>
                  <p>123 Education Hub, Medical District</p>
                  <p>New Delhi, India - 110001</p>
                </div>
              </div>
              
              <div className="contact-item">
                <Clock className="contact-icon" size={20} />
                <div>
                  <h4>Office Hours</h4>
                  <p>Monday - Saturday: 9:00 AM - 6:00 PM</p>
                  <p>Sunday: 10:00 AM - 4:00 PM</p>
                </div>
              </div>
            </div>
          </div>

          <div className="contact-form-container">
            {isSubmitted && (
              <div className="success-message">
                <h4>Thank you for your inquiry!</h4>
                <p>Our team will contact you within 24 hours to discuss your MBBS admission at FEFU.</p>
              </div>
            )}
            
            <form className="contact-form" onSubmit={handleSubmit(onSubmit)}>
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="firstName">First Name *</label>
                  <input
                    type="text"
                    id="firstName"
                    {...register('firstName', { required: 'First name is required' })}
                    className={errors.firstName ? 'error' : ''}
                  />
                  {errors.firstName && <span className="error-message">{errors.firstName.message}</span>}
                </div>
                
                <div className="form-group">
                  <label htmlFor="lastName">Last Name *</label>
                  <input
                    type="text"
                    id="lastName"
                    {...register('lastName', { required: 'Last name is required' })}
                    className={errors.lastName ? 'error' : ''}
                  />
                  {errors.lastName && <span className="error-message">{errors.lastName.message}</span>}
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="email">Email Address *</label>
                  <input
                    type="email"
                    id="email"
                    {...register('email', { 
                      required: 'Email is required',
                      pattern: {
                        value: /^\S+@\S+$/i,
                        message: 'Invalid email address'
                      }
                    })}
                    className={errors.email ? 'error' : ''}
                  />
                  {errors.email && <span className="error-message">{errors.email.message}</span>}
                </div>
                
                <div className="form-group">
                  <label htmlFor="phone">Phone Number *</label>
                  <input
                    type="tel"
                    id="phone"
                    {...register('phone', { required: 'Phone number is required' })}
                    className={errors.phone ? 'error' : ''}
                  />
                  {errors.phone && <span className="error-message">{errors.phone.message}</span>}
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="education">Current Education Level</label>
                <select id="education" {...register('education')}>
                  <option value="">Select your education level</option>
                  <option value="12th-appearing">12th Grade (Appearing)</option>
                  <option value="12th-completed">12th Grade (Completed)</option>
                  <option value="graduate">Graduate</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="neetScore">NEET Score (if applicable)</label>
                <input
                  type="text"
                  id="neetScore"
                  {...register('neetScore')}
                  placeholder="Enter your NEET score"
                />
              </div>

              <div className="form-group">
                <label htmlFor="message">Message</label>
                <textarea
                  id="message"
                  rows="4"
                  {...register('message')}
                  placeholder="Tell us about your goals and any specific questions you have about FEFU MBBS program..."
                ></textarea>
              </div>

              <button type="submit" className="submit-btn">
                Send Message
                <Send size={20} />
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ContactForm
