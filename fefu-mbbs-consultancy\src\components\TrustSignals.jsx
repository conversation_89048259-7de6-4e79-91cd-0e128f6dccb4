import React from 'react'
import { Award, Users, GraduationCap, Star, Shield, CheckCircle } from 'lucide-react'

const TrustSignals = () => {
  const achievements = [
    {
      icon: <Award size={32} />,
      title: "ISO 9001:2015 Certified",
      description: "Quality Management System Certified"
    },
    {
      icon: <Users size={32} />,
      title: "500+ Students Placed",
      description: "Successfully placed in last 3 years"
    },
    {
      icon: <GraduationCap size={32} />,
      title: "98% Success Rate",
      description: "Admission success rate"
    },
    {
      icon: <Star size={32} />,
      title: "4.9/5 Rating",
      description: "Based on 200+ reviews"
    }
  ]

  const certifications = [
    "Authorized FEFU Representative",
    "MCI Approved Consultant",
    "Government Recognized Agency",
    "AIIMS Alumni Founded"
  ]

  return (
    <section className="trust-signals">
      <div className="container">
        <div className="trust-content">
          <div className="trust-header">
            <Shield className="trust-icon" size={40} />
            <h3>Trusted by 500+ Students & Parents</h3>
            <p>Your success is our reputation. Here's why students choose us:</p>
          </div>

          <div className="achievements-grid">
            {achievements.map((achievement, index) => (
              <div key={index} className="achievement-card">
                <div className="achievement-icon">
                  {achievement.icon}
                </div>
                <h4>{achievement.title}</h4>
                <p>{achievement.description}</p>
              </div>
            ))}
          </div>

          <div className="certifications">
            <h4>Our Credentials & Certifications</h4>
            <div className="cert-list">
              {certifications.map((cert, index) => (
                <div key={index} className="cert-item">
                  <CheckCircle size={16} />
                  <span>{cert}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="recent-activity">
            <h4>Recent Success Stories</h4>
            <div className="activity-feed">
              <div className="activity-item">
                <div className="activity-dot"></div>
                <span><strong>Priya S.</strong> got admission to FEFU MBBS - 2 hours ago</span>
              </div>
              <div className="activity-item">
                <div className="activity-dot"></div>
                <span><strong>Rahul M.</strong> visa approved for FEFU - 5 hours ago</span>
              </div>
              <div className="activity-item">
                <div className="activity-dot"></div>
                <span><strong>Ananya K.</strong> started MBBS at FEFU - 1 day ago</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TrustSignals
