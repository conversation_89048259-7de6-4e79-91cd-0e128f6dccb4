import React, { useState } from 'react'
import { ChevronRight, ChevronLeft, Calendar, Clock, User, Phone, Mail, CheckCircle } from 'lucide-react'

const MultiStepBooking = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    consultationType: '',
    preferredDate: '',
    preferredTime: '',
    name: '',
    email: '',
    phone: '',
    neetScore: '',
    currentEducation: '',
    preferredCountry: 'russia',
    specificQuestions: ''
  })
  const [isSubmitted, setIsSubmitted] = useState(false)

  const steps = [
    {
      id: 1,
      title: "Consultation Type",
      description: "Choose the type of consultation you need"
    },
    {
      id: 2,
      title: "Schedule",
      description: "Select your preferred date and time"
    },
    {
      id: 3,
      title: "Personal Details",
      description: "Provide your contact information"
    },
    {
      id: 4,
      title: "Academic Background",
      description: "Tell us about your academic profile"
    },
    {
      id: 5,
      title: "Confirmation",
      description: "Review and confirm your booking"
    }
  ]

  const consultationTypes = [
    {
      id: 'general',
      title: 'General Consultation',
      description: 'Overview of MBBS abroad options',
      duration: '30 minutes',
      price: 'FREE'
    },
    {
      id: 'detailed',
      title: 'Detailed Assessment',
      description: 'Complete profile evaluation and guidance',
      duration: '60 minutes',
      price: 'FREE'
    },
    {
      id: 'premium',
      title: 'Premium Consultation',
      description: 'One-on-one with senior consultant',
      duration: '90 minutes',
      price: '₹2,000'
    }
  ]

  const timeSlots = [
    '09:00 AM', '10:00 AM', '11:00 AM', '12:00 PM',
    '02:00 PM', '03:00 PM', '04:00 PM', '05:00 PM',
    '06:00 PM', '07:00 PM'
  ]

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const submitBooking = () => {
    console.log('Booking submitted:', formData)
    setIsSubmitted(true)
  }

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.consultationType !== ''
      case 2:
        return formData.preferredDate !== '' && formData.preferredTime !== ''
      case 3:
        return formData.name !== '' && formData.email !== '' && formData.phone !== ''
      case 4:
        return formData.currentEducation !== ''
      default:
        return true
    }
  }

  if (isSubmitted) {
    return (
      <section className="multi-step-booking">
        <div className="container">
          <div className="booking-success">
            <CheckCircle className="success-icon" size={64} />
            <h2>Consultation Booked Successfully!</h2>
            <p>Thank you for booking your consultation with us. Here are your booking details:</p>
            
            <div className="booking-details">
              <div className="detail-item">
                <strong>Consultation Type:</strong>
                <span>{consultationTypes.find(type => type.id === formData.consultationType)?.title}</span>
              </div>
              <div className="detail-item">
                <strong>Date & Time:</strong>
                <span>{formData.preferredDate} at {formData.preferredTime}</span>
              </div>
              <div className="detail-item">
                <strong>Consultant:</strong>
                <span>Dr. Rajesh Kumar (Senior Consultant)</span>
              </div>
              <div className="detail-item">
                <strong>Meeting Link:</strong>
                <span>Will be sent via email 30 minutes before the session</span>
              </div>
            </div>

            <div className="next-steps">
              <h3>What's Next?</h3>
              <ul>
                <li>You'll receive a confirmation email within 5 minutes</li>
                <li>Our team will call you 1 day before to confirm</li>
                <li>Meeting link will be shared 30 minutes before</li>
                <li>Prepare your documents and questions</li>
              </ul>
            </div>

            <button 
              className="btn-primary"
              onClick={() => window.location.reload()}
            >
              Book Another Consultation
            </button>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="multi-step-booking">
      <div className="container">
        <div className="booking-header">
          <h2>Book Your Free MBBS Consultation</h2>
          <p>Get expert guidance in just a few simple steps</p>
        </div>

        <div className="booking-progress">
          {steps.map((step, index) => (
            <div key={step.id} className={`progress-step ${currentStep >= step.id ? 'active' : ''} ${currentStep > step.id ? 'completed' : ''}`}>
              <div className="step-number">
                {currentStep > step.id ? <CheckCircle size={20} /> : step.id}
              </div>
              <div className="step-info">
                <h4>{step.title}</h4>
                <p>{step.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="booking-form">
          {/* Step 1: Consultation Type */}
          {currentStep === 1 && (
            <div className="step-content">
              <h3>Choose Your Consultation Type</h3>
              <div className="consultation-types">
                {consultationTypes.map((type) => (
                  <div 
                    key={type.id}
                    className={`consultation-card ${formData.consultationType === type.id ? 'selected' : ''}`}
                    onClick={() => setFormData({...formData, consultationType: type.id})}
                  >
                    <div className="card-header">
                      <h4>{type.title}</h4>
                      <span className="price">{type.price}</span>
                    </div>
                    <p>{type.description}</p>
                    <div className="duration">
                      <Clock size={16} />
                      <span>{type.duration}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Step 2: Schedule */}
          {currentStep === 2 && (
            <div className="step-content">
              <h3>Select Date & Time</h3>
              <div className="schedule-grid">
                <div className="date-selection">
                  <label htmlFor="preferredDate">Preferred Date</label>
                  <input
                    type="date"
                    id="preferredDate"
                    name="preferredDate"
                    value={formData.preferredDate}
                    onChange={handleInputChange}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
                
                <div className="time-selection">
                  <label>Preferred Time</label>
                  <div className="time-slots">
                    {timeSlots.map((time) => (
                      <button
                        key={time}
                        type="button"
                        className={`time-slot ${formData.preferredTime === time ? 'selected' : ''}`}
                        onClick={() => setFormData({...formData, preferredTime: time})}
                      >
                        {time}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Personal Details */}
          {currentStep === 3 && (
            <div className="step-content">
              <h3>Your Contact Information</h3>
              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="name">Full Name *</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter your full name"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="email">Email Address *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="Enter your email"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="phone">Phone Number *</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="Enter your phone number"
                    required
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 4: Academic Background */}
          {currentStep === 4 && (
            <div className="step-content">
              <h3>Academic Background</h3>
              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="currentEducation">Current Education Level *</label>
                  <select
                    id="currentEducation"
                    name="currentEducation"
                    value={formData.currentEducation}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="">Select your education level</option>
                    <option value="12th-appearing">12th Grade (Appearing)</option>
                    <option value="12th-completed">12th Grade (Completed)</option>
                    <option value="graduate">Graduate</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div className="form-group">
                  <label htmlFor="neetScore">NEET Score (if applicable)</label>
                  <input
                    type="text"
                    id="neetScore"
                    name="neetScore"
                    value={formData.neetScore}
                    onChange={handleInputChange}
                    placeholder="Enter your NEET score"
                  />
                </div>

                <div className="form-group full-width">
                  <label htmlFor="specificQuestions">Specific Questions or Concerns</label>
                  <textarea
                    id="specificQuestions"
                    name="specificQuestions"
                    value={formData.specificQuestions}
                    onChange={handleInputChange}
                    placeholder="Tell us about your specific questions or concerns..."
                    rows="4"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 5: Confirmation */}
          {currentStep === 5 && (
            <div className="step-content">
              <h3>Confirm Your Booking</h3>
              <div className="booking-summary">
                <div className="summary-section">
                  <h4>Consultation Details</h4>
                  <p><strong>Type:</strong> {consultationTypes.find(type => type.id === formData.consultationType)?.title}</p>
                  <p><strong>Date:</strong> {formData.preferredDate}</p>
                  <p><strong>Time:</strong> {formData.preferredTime}</p>
                </div>

                <div className="summary-section">
                  <h4>Contact Information</h4>
                  <p><strong>Name:</strong> {formData.name}</p>
                  <p><strong>Email:</strong> {formData.email}</p>
                  <p><strong>Phone:</strong> {formData.phone}</p>
                </div>

                <div className="summary-section">
                  <h4>Academic Background</h4>
                  <p><strong>Education:</strong> {formData.currentEducation}</p>
                  {formData.neetScore && <p><strong>NEET Score:</strong> {formData.neetScore}</p>}
                </div>
              </div>
            </div>
          )}

          <div className="form-navigation">
            {currentStep > 1 && (
              <button type="button" className="btn-secondary" onClick={prevStep}>
                <ChevronLeft size={20} />
                Previous
              </button>
            )}

            {currentStep < steps.length ? (
              <button 
                type="button" 
                className="btn-primary" 
                onClick={nextStep}
                disabled={!isStepValid()}
              >
                Next
                <ChevronRight size={20} />
              </button>
            ) : (
              <button 
                type="button" 
                className="btn-primary" 
                onClick={submitBooking}
                disabled={!isStepValid()}
              >
                Confirm Booking
                <CheckCircle size={20} />
              </button>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}

export default MultiStepBooking
