import React, { useState } from 'react'
import { Menu, X, Phone, Mail } from 'lucide-react'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setIsMenuOpen(false)
  }

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          <div className="logo">
            <h2>FEFU MBBS Consultancy</h2>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="desktop-nav">
            <ul>
              <li><button onClick={() => scrollToSection('home')}>Home</button></li>
              <li><button onClick={() => scrollToSection('about')}>About FEFU</button></li>
              <li><button onClick={() => scrollToSection('services')}>Services</button></li>
              <li><button onClick={() => scrollToSection('contact')}>Contact</button></li>
            </ul>
          </nav>

          {/* Contact Info */}
          <div className="header-contact">
            <div className="contact-item">
              <Phone size={16} />
              <span>+91 9876543210</span>
            </div>
            <div className="contact-item">
              <Mail size={16} />
              <span><EMAIL></span>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button className="mobile-menu-btn" onClick={toggleMenu}>
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="mobile-nav">
            <ul>
              <li><button onClick={() => scrollToSection('home')}>Home</button></li>
              <li><button onClick={() => scrollToSection('about')}>About FEFU</button></li>
              <li><button onClick={() => scrollToSection('services')}>Services</button></li>
              <li><button onClick={() => scrollToSection('contact')}>Contact</button></li>
            </ul>
            <div className="mobile-contact">
              <div className="contact-item">
                <Phone size={16} />
                <span>+91 9876543210</span>
              </div>
              <div className="contact-item">
                <Mail size={16} />
                <span><EMAIL></span>
              </div>
            </div>
          </nav>
        )}
      </div>
    </header>
  )
}

export default Header
