import React from 'react'
import { Award, BookOpen, Users, Star, Linkedin, Mail, Phone } from 'lucide-react'

const ConsultantProfiles = () => {
  const consultants = [
    {
      id: 1,
      name: "Dr. <PERSON><PERSON>",
      title: "Chief Medical Consultant",
      experience: "15+ Years",
      education: "MBBS, MD - AIIMS Delhi",
      specialization: "Medical Education Counseling",
      image: "/api/placeholder/150/150",
      achievements: [
        "Former AIIMS Faculty Member",
        "500+ Students Successfully Placed",
        "Expert in Russian Medical Education",
        "MCI Approved Counselor"
      ],
      stats: {
        studentsPlaced: 500,
        successRate: 98,
        experience: 15
      },
      contact: {
        email: "<EMAIL>",
        phone: "+91 **********",
        linkedin: "#"
      },
      bio: "Dr. <PERSON> is a renowned medical education consultant with over 15 years of experience in guiding students for MBBS abroad. He has personally visited FEFU multiple times and maintains strong relationships with university officials."
    },
    {
      id: 2,
      name: "Ms. <PERSON><PERSON>",
      title: "Senior Admission Counselor",
      experience: "10+ Years",
      education: "MBA - International Business",
      specialization: "Visa & Documentation",
      image: "/api/placeholder/150/150",
      achievements: [
        "Visa Success Rate: 99%",
        "300+ Successful Visa Applications",
        "Expert in Russian Immigration",
        "Certified Education Counselor"
      ],
      stats: {
        studentsPlaced: 300,
        successRate: 99,
        experience: 10
      },
      contact: {
        email: "<EMAIL>",
        phone: "+91 **********",
        linkedin: "#"
      },
      bio: "Ms. Priya Sharma specializes in visa processing and documentation. Her expertise has helped hundreds of students secure their Russian student visas with an exceptional success rate."
    },
    {
      id: 3,
      name: "Mr. Arjun Patel",
      title: "Student Support Manager",
      experience: "8+ Years",
      education: "Masters in Counseling Psychology",
      specialization: "Student Welfare & Support",
      image: "/api/placeholder/150/150",
      achievements: [
        "24/7 Student Support Specialist",
        "Crisis Management Expert",
        "Cultural Integration Specialist",
        "Alumni Network Coordinator"
      ],
      stats: {
        studentsPlaced: 200,
        successRate: 96,
        experience: 8
      },
      contact: {
        email: "<EMAIL>",
        phone: "+91 9876543212",
        linkedin: "#"
      },
      bio: "Mr. Arjun Patel ensures that our students receive comprehensive support throughout their journey. He coordinates with our alumni network and provides ongoing assistance to students at FEFU."
    }
  ]

  return (
    <section className="consultant-profiles">
      <div className="container">
        <div className="section-header">
          <h2>Meet Our Expert Consultants</h2>
          <p>Experienced professionals dedicated to your MBBS success</p>
        </div>

        <div className="consultants-grid">
          {consultants.map((consultant) => (
            <div key={consultant.id} className="consultant-card">
              <div className="consultant-header">
                <div className="consultant-image">
                  <img src={consultant.image} alt={consultant.name} />
                  <div className="experience-badge">
                    {consultant.experience}
                  </div>
                </div>
                
                <div className="consultant-info">
                  <h3>{consultant.name}</h3>
                  <p className="consultant-title">{consultant.title}</p>
                  <p className="consultant-education">{consultant.education}</p>
                  <p className="consultant-specialization">{consultant.specialization}</p>
                </div>
              </div>

              <div className="consultant-stats">
                <div className="stat-item">
                  <Users size={20} />
                  <div>
                    <span className="stat-number">{consultant.stats.studentsPlaced}+</span>
                    <span className="stat-label">Students Placed</span>
                  </div>
                </div>
                
                <div className="stat-item">
                  <Star size={20} />
                  <div>
                    <span className="stat-number">{consultant.stats.successRate}%</span>
                    <span className="stat-label">Success Rate</span>
                  </div>
                </div>
                
                <div className="stat-item">
                  <Award size={20} />
                  <div>
                    <span className="stat-number">{consultant.stats.experience}</span>
                    <span className="stat-label">Years Experience</span>
                  </div>
                </div>
              </div>

              <div className="consultant-bio">
                <p>{consultant.bio}</p>
              </div>

              <div className="consultant-achievements">
                <h4>Key Achievements</h4>
                <ul>
                  {consultant.achievements.map((achievement, index) => (
                    <li key={index}>{achievement}</li>
                  ))}
                </ul>
              </div>

              <div className="consultant-contact">
                <h4>Get in Touch</h4>
                <div className="contact-methods">
                  <a href={`mailto:${consultant.contact.email}`} className="contact-method">
                    <Mail size={16} />
                    <span>Email</span>
                  </a>
                  <a href={`tel:${consultant.contact.phone}`} className="contact-method">
                    <Phone size={16} />
                    <span>Call</span>
                  </a>
                  <a href={consultant.contact.linkedin} className="contact-method">
                    <Linkedin size={16} />
                    <span>LinkedIn</span>
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="team-credentials">
          <div className="credentials-content">
            <h3>Our Team's Collective Expertise</h3>
            <div className="credentials-stats">
              <div className="credential-item">
                <BookOpen size={32} />
                <div>
                  <h4>1000+ Students Guided</h4>
                  <p>Successfully placed in top medical universities</p>
                </div>
              </div>
              
              <div className="credential-item">
                <Award size={32} />
                <div>
                  <h4>98% Overall Success Rate</h4>
                  <p>Consistently high admission and visa approval rates</p>
                </div>
              </div>
              
              <div className="credential-item">
                <Users size={32} />
                <div>
                  <h4>30+ Years Combined Experience</h4>
                  <p>Deep expertise in medical education consulting</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="consultation-cta">
          <h3>Ready to Work with Our Experts?</h3>
          <p>Book a free consultation with our experienced team</p>
          <button 
            className="btn-primary"
            onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}
          >
            Schedule Free Consultation
          </button>
        </div>
      </div>
    </section>
  )
}

export default ConsultantProfiles
