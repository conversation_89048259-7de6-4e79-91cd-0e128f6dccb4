import React, { useState } from 'react'
import { Calculator, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react'

const NEETEvaluator = () => {
  const [formData, setFormData] = useState({
    neetScore: '',
    category: '',
    state: '',
    budget: ''
  })
  const [result, setResult] = useState(null)
  const [showResult, setShowResult] = useState(false)

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const evaluateEligibility = () => {
    const score = parseInt(formData.neetScore)
    let eligibility = 'Not Eligible'
    let recommendation = ''
    let color = 'red'

    if (score >= 400) {
      eligibility = 'Highly Eligible'
      recommendation = 'Excellent! You have a very high chance of getting admission to FEFU MBBS program.'
      color = 'green'
    } else if (score >= 300) {
      eligibility = 'Eligible'
      recommendation = 'Good! You meet the eligibility criteria for FEFU MBBS program.'
      color = 'orange'
    } else if (score >= 200) {
      eligibility = 'Conditionally Eligible'
      recommendation = 'You may be eligible for FEFU MBBS program. Let us evaluate your complete profile.'
      color = 'yellow'
    } else {
      eligibility = 'Need Consultation'
      recommendation = 'Let our experts review your profile for alternative pathways to MBBS.'
      color = 'red'
    }

    setResult({
      eligibility,
      recommendation,
      color,
      score
    })
    setShowResult(true)
  }

  const resetEvaluator = () => {
    setFormData({
      neetScore: '',
      category: '',
      state: '',
      budget: ''
    })
    setResult(null)
    setShowResult(false)
  }

  return (
    <section className="neet-evaluator">
      <div className="container">
        <div className="evaluator-content">
          <div className="evaluator-header">
            <Calculator className="evaluator-icon" size={40} />
            <h3>Free NEET Score Evaluation</h3>
            <p>Get instant assessment of your FEFU MBBS admission chances</p>
          </div>

          {!showResult ? (
            <div className="evaluator-form">
              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="neetScore">NEET Score *</label>
                  <input
                    type="number"
                    id="neetScore"
                    name="neetScore"
                    value={formData.neetScore}
                    onChange={handleInputChange}
                    placeholder="Enter your NEET score"
                    min="0"
                    max="720"
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="category">Category</label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Category</option>
                    <option value="general">General</option>
                    <option value="obc">OBC</option>
                    <option value="sc">SC</option>
                    <option value="st">ST</option>
                  </select>
                </div>

                <div className="form-group">
                  <label htmlFor="state">State</label>
                  <input
                    type="text"
                    id="state"
                    name="state"
                    value={formData.state}
                    onChange={handleInputChange}
                    placeholder="Your state"
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="budget">Budget (Annual)</label>
                  <select
                    id="budget"
                    name="budget"
                    value={formData.budget}
                    onChange={handleInputChange}
                  >
                    <option value="">Select Budget Range</option>
                    <option value="3-5">₹3-5 Lakhs</option>
                    <option value="5-8">₹5-8 Lakhs</option>
                    <option value="8-12">₹8-12 Lakhs</option>
                    <option value="12+">₹12+ Lakhs</option>
                  </select>
                </div>
              </div>

              <button 
                className="evaluate-btn"
                onClick={evaluateEligibility}
                disabled={!formData.neetScore}
              >
                Evaluate My Chances
                <ArrowRight size={20} />
              </button>
            </div>
          ) : (
            <div className="evaluation-result">
              <div className={`result-card ${result.color}`}>
                <div className="result-header">
                  {result.color === 'green' || result.color === 'orange' ? 
                    <CheckCircle size={32} /> : 
                    <AlertCircle size={32} />
                  }
                  <h4>Evaluation Result</h4>
                </div>
                
                <div className="result-content">
                  <div className="eligibility-status">
                    <span className="status-label">Status:</span>
                    <span className={`status-value ${result.color}`}>{result.eligibility}</span>
                  </div>
                  
                  <p className="recommendation">{result.recommendation}</p>
                  
                  <div className="next-steps">
                    <h5>Recommended Next Steps:</h5>
                    <ul>
                      <li>Book a free consultation with our experts</li>
                      <li>Get detailed admission guidance</li>
                      <li>Receive personalized university recommendations</li>
                      <li>Start your application process</li>
                    </ul>
                  </div>
                </div>

                <div className="result-actions">
                  <button className="btn-primary" onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}>
                    Book Free Consultation
                  </button>
                  <button className="btn-secondary" onClick={resetEvaluator}>
                    Evaluate Again
                  </button>
                </div>
              </div>
            </div>
          )}

          <div className="evaluator-disclaimer">
            <p><small>* This is a preliminary evaluation. Final admission depends on various factors including document verification and university requirements.</small></p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default NEETEvaluator
