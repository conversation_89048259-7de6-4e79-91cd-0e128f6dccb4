{"name": "react-modal", "version": "3.16.3", "description": "Accessible modal dialog component for React.JS", "main": "./lib/index.js", "module": "./lib/index.js", "repository": {"type": "git", "url": "https://github.com/reactjs/react-modal.git"}, "homepage": "https://github.com/reactjs/react-modal", "bugs": "https://github.com/reactjs/react-modal/issues", "directories": {"example": "examples"}, "scripts": {"start": "npx webpack-dev-server --config ./scripts/webpack.config.js --inline --host 127.0.0.1 --content-base examples/", "test": "cross-env NODE_ENV=test karma start", "lint": "eslint src/"}, "authors": ["<PERSON>"], "license": "MIT", "devDependencies": {"@webcomponents/custom-elements": "^1.5.0", "babel-cli": "^6.26.0", "babel-core": "^6.25.0", "babel-eslint": "^8.0.1", "babel-loader": "^7.1.2", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "^1.6.0", "babel-preset-react": "^6.24.1", "babel-preset-stage-2": "^6.24.1", "coveralls": "^3.1.0", "cross-env": "^5.2.1", "eslint": "^4.8.0", "eslint-config-prettier": "^2.6.0", "eslint-import-resolver-webpack": "^0.9.0", "eslint-plugin-import": "^2.23.2", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^2.3.1", "eslint-plugin-react": "^7.23.2", "istanbul-instrumenter-loader": "^3.0.0", "karma": "^6.3.6", "karma-chrome-launcher": "2.2.0", "karma-coverage": "^2.0.3", "karma-firefox-launcher": "1.0.1", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.1", "karma-sourcemap-loader": "^0.3.8", "karma-webpack": "^2.0.4", "mocha": "^8.4.0", "npm-run-all": "^4.1.1", "prettier": "^1.19.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-router": "^4.2.0", "react-router-dom": "^4.2.2", "should": "^13.1.0", "sinon": "next", "uglify-js": "3.1.1", "webpack": "^4.46.0", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.2"}, "dependencies": {"exenv": "^1.2.0", "prop-types": "^15.7.2", "react-lifecycles-compat": "^3.0.0", "warning": "^4.0.3"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0 || ^16 || ^17 || ^18 || ^19", "react-dom": "^0.14.0 || ^15.0.0 || ^16 || ^17 || ^18 || ^19"}, "tags": ["react", "modal", "dialog"], "keywords": ["react", "react-component", "modal", "dialog"]}