import React, { useState, useEffect } from 'react'
import { X, <PERSON>, <PERSON>, <PERSON>, ArrowRight } from 'lucide-react'

const ExitIntentPopup = () => {
  const [showPopup, setShowPopup] = useState(false)
  const [hasShown, setHasShown] = useState(false)

  useEffect(() => {
    const handleMouseLeave = (e) => {
      // Only trigger if mouse leaves from the top of the page and popup hasn't been shown
      if (e.clientY <= 0 && !hasShown) {
        setShowPopup(true)
        setHasShown(true)
      }
    }

    // Also show popup after 30 seconds if user hasn't interacted
    const timer = setTimeout(() => {
      if (!hasShown) {
        setShowPopup(true)
        setHasShown(true)
      }
    }, 30000)

    document.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave)
      clearTimeout(timer)
    }
  }, [hasShown])

  const closePopup = () => {
    setShowPopup(false)
  }

  const handleClaim = () => {
    // Scroll to contact form
    document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })
    setShowPopup(false)
  }

  if (!showPopup) return null

  return (
    <div className="exit-intent-overlay">
      <div className="exit-intent-popup">
        <button className="close-popup" onClick={closePopup}>
          <X size={24} />
        </button>

        <div className="popup-content">
          <div className="popup-header">
            <Gift className="popup-icon" size={48} />
            <h3>Wait! Don't Miss This Exclusive Offer</h3>
            <p>Before you go, claim your special FEFU MBBS admission package</p>
          </div>

          <div className="offer-details">
            <div className="offer-badge">
              <span className="discount">50% OFF</span>
              <span className="offer-text">Limited Time Offer</span>
            </div>

            <div className="offer-benefits">
              <h4>🎁 What You Get FREE:</h4>
              <ul>
                <li>✅ Complete Admission Guidance (Worth ₹25,000)</li>
                <li>✅ Visa Processing Support (Worth ₹50,000)</li>
                <li>✅ Document Preparation (Worth ₹15,000)</li>
                <li>✅ Pre-departure Training (Worth ₹10,000)</li>
                <li>✅ 24/7 Student Support (Worth ₹20,000)</li>
              </ul>
              
              <div className="total-value">
                <span className="original-price">Total Value: ₹1,20,000</span>
                <span className="offer-price">Your Price: ₹60,000</span>
              </div>
            </div>

            <div className="urgency-timer">
              <Clock size={20} />
              <span>This offer expires in 24 hours!</span>
            </div>

            <div className="social-proof">
              <div className="proof-item">
                <Star className="star-icon" size={16} />
                <span>500+ students already claimed this offer</span>
              </div>
              <div className="proof-item">
                <span className="recent-activity">🔥 3 students claimed this offer in the last hour</span>
              </div>
            </div>
          </div>

          <div className="popup-actions">
            <button className="claim-offer-btn" onClick={handleClaim}>
              <Gift size={20} />
              Claim My Offer Now
              <ArrowRight size={20} />
            </button>
            
            <button className="maybe-later-btn" onClick={closePopup}>
              Maybe Later
            </button>
          </div>

          <div className="popup-footer">
            <p>⚡ Limited spots available - Only 10 offers left this month!</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ExitIntentPopup
