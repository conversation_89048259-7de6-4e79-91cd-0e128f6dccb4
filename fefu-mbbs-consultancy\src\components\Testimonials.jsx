import React, { useState } from 'react'
import { Star, Quote, ChevronLeft, ChevronRight, Play, MapPin } from 'lucide-react'

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0)

  const testimonials = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      location: "Delhi, India",
      course: "MBBS 3rd Year, FEFU",
      neetScore: 485,
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "Thanks to FEFU MBBS Consultancy, I'm now in my 3rd year at FEFU! Their guidance was exceptional from NEET counseling to visa processing. The team supported me throughout my journey, and now I'm living my dream of becoming a doctor.",
      videoUrl: "#",
      achievement: "Secured admission with 485 NEET score"
    },
    {
      id: 2,
      name: "<PERSON><PERSON>",
      location: "Gujarat, India",
      course: "MBBS 2nd Year, FEFU",
      neetScore: 420,
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "I was worried about my NEET score, but the consultancy team assured me and helped me get admission to FEFU. The university is amazing, and the education quality is world-class. I'm grateful for their support!",
      videoUrl: "#",
      achievement: "Successfully placed despite average NEET score"
    },
    {
      id: 3,
      name: "<PERSON>nya <PERSON>",
      location: "Hyderabad, India",
      course: "MBBS 1st Year, FEFU",
      neetScore: 510,
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "The entire process was smooth and transparent. From documentation to accommodation, everything was handled professionally. FEFU has excellent facilities and the faculty is very supportive of international students.",
      videoUrl: "#",
      achievement: "Completed admission process in record time"
    },
    {
      id: 4,
      name: "Arjun Singh",
      location: "Punjab, India",
      course: "MBBS Graduate, FEFU",
      neetScore: 445,
      image: "/api/placeholder/80/80",
      rating: 5,
      text: "I graduated from FEFU last year and now I'm preparing for FMGE. The education I received was top-notch, and the consultancy's support didn't end with admission - they helped throughout my 6-year journey!",
      videoUrl: "#",
      achievement: "Successfully graduated and preparing for FMGE"
    }
  ]

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const current = testimonials[currentTestimonial]

  return (
    <section className="testimonials">
      <div className="container">
        <div className="section-header">
          <h2>Success Stories from Our Students</h2>
          <p>Real students, real success stories from FEFU MBBS program</p>
        </div>

        <div className="testimonial-showcase">
          <div className="testimonial-main">
            <div className="testimonial-card">
              <div className="quote-icon">
                <Quote size={32} />
              </div>
              
              <div className="testimonial-content">
                <div className="rating">
                  {[...Array(current.rating)].map((_, i) => (
                    <Star key={i} size={16} fill="currentColor" />
                  ))}
                </div>
                
                <p className="testimonial-text">"{current.text}"</p>
                
                <div className="student-info">
                  <div className="student-avatar">
                    <img src={current.image} alt={current.name} />
                    <div className="video-overlay" onClick={() => console.log('Play video')}>
                      <Play size={20} />
                    </div>
                  </div>
                  
                  <div className="student-details">
                    <h4>{current.name}</h4>
                    <p className="course">{current.course}</p>
                    <div className="location">
                      <MapPin size={14} />
                      <span>{current.location}</span>
                    </div>
                    <div className="achievement">
                      <span className="neet-score">NEET: {current.neetScore}</span>
                      <span className="achievement-text">{current.achievement}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="testimonial-controls">
              <button className="control-btn" onClick={prevTestimonial}>
                <ChevronLeft size={20} />
              </button>
              
              <div className="testimonial-indicators">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    className={`indicator ${index === currentTestimonial ? 'active' : ''}`}
                    onClick={() => setCurrentTestimonial(index)}
                  />
                ))}
              </div>
              
              <button className="control-btn" onClick={nextTestimonial}>
                <ChevronRight size={20} />
              </button>
            </div>
          </div>

          <div className="testimonial-stats">
            <h3>Student Success Metrics</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <div className="stat-number">500+</div>
                <div className="stat-label">Students Placed</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">98%</div>
                <div className="stat-label">Success Rate</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">95%</div>
                <div className="stat-label">Visa Approval</div>
              </div>
              <div className="stat-item">
                <div className="stat-number">4.9/5</div>
                <div className="stat-label">Student Rating</div>
              </div>
            </div>

            <div className="recent-admissions">
              <h4>Recent Admissions (This Month)</h4>
              <div className="admission-list">
                <div className="admission-item">
                  <div className="admission-dot"></div>
                  <span>15 students got FEFU admission</span>
                </div>
                <div className="admission-item">
                  <div className="admission-dot"></div>
                  <span>12 visa approvals received</span>
                </div>
                <div className="admission-item">
                  <div className="admission-dot"></div>
                  <span>8 students started their journey</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="testimonial-cta">
          <h3>Ready to Join Our Success Stories?</h3>
          <p>Get expert guidance and join 500+ successful MBBS students</p>
          <button 
            className="btn-primary"
            onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}
          >
            Start Your MBBS Journey
          </button>
        </div>
      </div>
    </section>
  )
}

export default Testimonials
